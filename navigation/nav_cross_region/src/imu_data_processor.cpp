#include "imu_data_processor.hpp"

#include "utils/time.hpp"

#include <algorithm>
#include <cmath>
#include <fstream>
#include <iomanip>
#include <sstream>
#include <thread>

namespace fescue_iox
{

ImuDataProcessor::ImuDataProcessor(const ImuProcessorParam &param)
    : param_(param)
    , last_imu_time_(std::chrono::steady_clock::now())
{
    LOG_INFO("[ImuDataProcessor] Initialize IMU data processor");
    bias_samples_.reserve(param_.bias_calibration_samples);
}

ImuDataProcessor::~ImuDataProcessor()
{
    Shutdown();
    LOG_INFO("[ImuDataProcessor] IMU data processor shutdown");
}

void ImuDataProcessor::Initialize()
{
    LOG_INFO("[ImuDataProcessor] Initialize IMU data processing system");

    // 初始化数据日志
    if (param_.enable_data_logging)
    {
        InitializeDataLogging();
    }

    processing_active_.store(true);
    LOG_INFO("[ImuDataProcessor] IMU data processor initialized successfully");
}

void ImuDataProcessor::Shutdown()
{
    LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] Shutting down IMU data processor");

    // 停止旋转控制
    StopRotationControl();

    // 停止处理
    processing_active_.store(false);

    // 关闭数据日志
    CloseDataLogging();
}

void ImuDataProcessor::SetImuData(const ImuData &imu_data)
{
    if (!processing_active_.load())
    {
        return;
    }

    std::lock_guard<std::mutex> lock(imu_data_mutex_);
    ProcessImuData(imu_data);
}

void ImuDataProcessor::SetMotorSpeedData(const float &act_linear, const float &act_angular)
{
    if (!processing_active_.load())
    {
        return;
    }

    std::lock_guard<std::mutex> lock(motor_speed_mutex_);
    act_linear_ = act_linear;
    act_angular_ = act_angular;
}

void ImuDataProcessor::SetImuDataCallback(std::function<void(const ImuData &)> callback)
{
    imu_data_callback_ = callback;
}

void ImuDataProcessor::SetVelocityCallback(std::function<void(float, float, uint64_t)> callback)
{
    velocity_callback_ = callback;
}

void ImuDataProcessor::ProcessImuData(const ImuData &imu_data)
{
    auto current_time = std::chrono::steady_clock::now();
    float dt = 0.0f;

    if (!is_first_imu_)
    {
        dt = std::chrono::duration<float>(current_time - last_imu_time_).count();
    }
    else
    {
        is_first_imu_ = false;
        LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] Ignore first IMU data");
        last_imu_time_ = current_time;
        last_imu_timestamp_ = imu_data.system_timestamp;
        return;
    }

    last_imu_time_ = current_time;
    last_imu_timestamp_ = imu_data.system_timestamp;

    // IMU零偏校准
    if (!is_bias_calibrated_)
    {
        CalibrateImuBias(imu_data);
        return;
    }

    // 应用零偏校正
    float angular_velocity_z = imu_data.angular_velocity_z - bias_z_;

    // 保存原始数据用于日志
    float raw_angular_velocity = angular_velocity_z;

    // 初始化滤波器（仅在首次使用时）
    if (!filter_initialized_)
    {
        InitializeFilters(angular_velocity_z);
        filter_initialized_ = true;
    }

    // 应用低通滤波
    angular_velocity_z = ApplyLowPassFilter(angular_velocity_z, filtered_angular_velocity_, param_.filter_alpha);

    // 记录滤波前后数据
    if (param_.enable_data_logging)
    {
        LogFilteringData(imu_data.system_timestamp, raw_angular_velocity, angular_velocity_z);
    }

    // 应用阈值滤波
    if (std::abs(angular_velocity_z) < param_.angular_velocity_threshold)
    {
        angular_velocity_z = 0.0f;
    }

    // 更新当前航向角 (左手坐标系：正角速度表示顺时针旋转)
    current_yaw_ += angular_velocity_z * dt;

    // 归一化航向角到 [-π, π] 范围
    current_yaw_ = NormalizeAngle(current_yaw_);

    // 更新旋转控制
    if (rotation_control_active_.load())
    {
        UpdateRotationControl(angular_velocity_z, dt);
    }

    // 更新线性运动控制
    if (linear_motion_control_active_.load())
    {
        UpdateLinearMotionControl(angular_velocity_z, dt);
    }

    // 更新持续线性运动控制
    if (continuous_linear_motion_active_.load())
    {
        UpdateContinuousLinearMotion(angular_velocity_z, dt);
    }

    // 更新位置估计 (如果有线性运动)
    if (linear_motion_control_active_.load() || continuous_linear_motion_active_.load())
    {
        float current_expect_linear_velocity = 0.0f;
        if (linear_motion_control_active_.load())
        {
            current_expect_linear_velocity = expect_linear_velocity_;
        }
        else if (continuous_linear_motion_active_.load())
        {
            current_expect_linear_velocity = continuous_expect_linear_velocity_;
        }
        UpdatePositionEstimate(current_expect_linear_velocity, angular_velocity_z, dt);
    }

    // 创建处理后的IMU数据并通过回调传递
    if (imu_data_callback_)
    {
        ImuData processed_data = imu_data;
        processed_data.angular_velocity_z = angular_velocity_z;
        imu_data_callback_(processed_data);
    }
}

void ImuDataProcessor::CalibrateImuBias(const ImuData &imu_data)
{
    if (bias_samples_.size() < param_.bias_calibration_samples)
    {
        bias_samples_.push_back(imu_data.angular_velocity_z);
        LOG_INFO("[ImuDataProcessor] Collecting bias sample: {}/{}",
                 bias_samples_.size(), param_.bias_calibration_samples);
        return;
    }

    // 计算平均零偏
    float sum = 0.0f;
    for (const auto &sample : bias_samples_)
    {
        sum += sample;
    }
    bias_z_ = sum / bias_samples_.size();
    is_bias_calibrated_ = true;
    bias_samples_.clear();

    LOG_INFO("[ImuDataProcessor] Bias calibration completed, bias_z = {:.6f} rad/s", bias_z_);
}

float ImuDataProcessor::ApplyLowPassFilter(float new_value, float &filtered_value, float alpha)
{
    // 一阶低通滤波器: filtered_value = alpha * new_value + (1 - alpha) * filtered_value
    // alpha越小，滤波越强（更平滑但响应更慢）
    filtered_value = alpha * new_value + (1.0f - alpha) * filtered_value;
    return filtered_value;
}

void ImuDataProcessor::InitializeFilters(float initial_angular_velocity)
{
    filtered_angular_velocity_ = initial_angular_velocity;
    LOG_INFO("[ImuDataProcessor] Filter initialized: angular_velocity={:.4f}", initial_angular_velocity);
}

RotationControlResult ImuDataProcessor::StartRotationControl(float target_angle, float angular_velocity)
{
    if (rotation_control_active_.load())
    {
        LOG_WARN("[ImuDataProcessor] Rotation control already active");
        return rotation_result_;
    }

    {
        std::lock_guard<std::mutex> lock(rotation_control_mutex_);
        // 初始化旋转控制状态
        target_rotation_angle_ = target_angle;
        target_angular_velocity_ = angular_velocity;
        accumulated_rotation_ = 0.0f;

        rotation_start_time_ = std::chrono::steady_clock::now();
        current_rotation_start_time_ = rotation_start_time_;

        rotation_result_ = RotationControlResult{};
        rotation_result_.target_rotation = target_angle;

        // 初始化后退状态
        backup_attempt_count_ = 0;
        is_backing_up_ = false;
    }

    rotation_control_active_.store(true);

    // 启动旋转控制线程
    if (rotation_control_thread_.joinable())
    {
        rotation_control_thread_.join();
    }
    rotation_control_thread_ = std::thread(&ImuDataProcessor::RotationControlThread, this);

    LOG_INFO("[ImuDataProcessor] Started rotation control: target={:.3f}°, velocity={:.3f}°/s, velocity={:.3f}rad/s",
             target_angle * 180.0f / M_PI, angular_velocity * 180.0f / M_PI, angular_velocity);

    return rotation_result_;
}

void ImuDataProcessor::StopRotationControl()
{
    if (rotation_control_active_.load())
    {
        rotation_control_active_.store(false);

        if (rotation_control_thread_.joinable())
        {
            rotation_control_thread_.join();
        }

        LOG_INFO("[ImuDataProcessor] Rotation control stopped");
    }
}

bool ImuDataProcessor::IsRotationControlActive() const
{
    return rotation_control_active_.load();
}

LinearMotionControlResult ImuDataProcessor::StartLinearMotionControl(float target_distance, float expect_velocity, float target_heading)
{
    if (!is_bias_calibrated_)
    {
        LOG_ERROR("[ImuDataProcessor] Cannot start linear motion control: IMU bias not calibrated");
        LinearMotionControlResult result;
        result.completed = false;
        result.timeout = true;
        return result;
    }

    // 停止之前的线性运动控制
    StopLinearMotionControl();

    // 初始化线性运动控制状态
    {
        std::lock_guard<std::mutex> lock(linear_motion_control_mutex_);
        target_distance_ = target_distance;
        target_heading_ = target_heading;
        expect_linear_velocity_ = expect_velocity;
        accumulated_distance_ = 0.0f;
        linear_motion_accumulated_rotation_ = 0.0f;
        initial_heading_ = current_yaw_;

        linear_motion_result_ = LinearMotionControlResult{};
        linear_motion_result_.target_distance = target_distance;
        linear_motion_result_.completed = false;
        linear_motion_result_.timeout = false;
    }

    linear_motion_start_time_ = std::chrono::steady_clock::now();
    linear_motion_control_active_.store(true);

    // 启动线性运动控制线程
    if (linear_motion_control_thread_.joinable())
    {
        linear_motion_control_thread_.join();
    }
    linear_motion_control_thread_ = std::thread(&ImuDataProcessor::LinearMotionControlThread, this);

    LOG_INFO("[ImuDataProcessor] Started linear motion control: target_distance={:.3f}m, velocity={:.3f}m/s, target_heading={:.1f}°",
             target_distance, expect_velocity, target_heading * 180.0f / M_PI);

    return linear_motion_result_;
}

void ImuDataProcessor::StopLinearMotionControl()
{
    if (linear_motion_control_active_.load())
    {
        linear_motion_control_active_.store(false);

        if (linear_motion_control_thread_.joinable())
        {
            linear_motion_control_thread_.join();
        }

        LOG_INFO("[ImuDataProcessor] Linear motion control stopped");
    }
}

bool ImuDataProcessor::IsLinearMotionControlActive() const
{
    return linear_motion_control_active_.load();
}

void ImuDataProcessor::StartContinuousLinearMotion(float expect_linear_velocity, float target_heading)
{
    if (continuous_linear_motion_active_.load())
    {
        LOG_WARN("[ImuDataProcessor] Continuous linear motion already active");
        return;
    }

    {
        std::lock_guard<std::mutex> lock(continuous_linear_motion_mutex_);
        continuous_target_heading_ = target_heading;
        continuous_expect_linear_velocity_ = expect_linear_velocity;
        continuous_linear_motion_rotation_ = 0.0f;
    }

    continuous_linear_motion_active_.store(true);

    // 启动持续线性运动控制线程
    if (continuous_linear_motion_thread_.joinable())
    {
        continuous_linear_motion_thread_.join();
    }
    continuous_linear_motion_thread_ = std::thread(&ImuDataProcessor::ContinuousLinearMotionThread, this);

    LOG_INFO("[ImuDataProcessor] Started continuous linear motion: velocity={:.3f}m/s, target_heading={:.1f}°",
             expect_linear_velocity, target_heading * 180.0f / M_PI);
}

void ImuDataProcessor::StopContinuousLinearMotion()
{
    if (continuous_linear_motion_active_.load())
    {
        continuous_linear_motion_active_.store(false);

        if (continuous_linear_motion_thread_.joinable())
        {
            continuous_linear_motion_thread_.join();
        }

        LOG_INFO("[ImuDataProcessor] Continuous linear motion stopped");
    }
}

bool ImuDataProcessor::IsContinuousLinearMotionActive() const
{
    return continuous_linear_motion_active_.load();
}

void ImuDataProcessor::RotationControlThread()
{
    LOG_INFO("[ImuDataProcessor] Rotation control thread started");

    while (rotation_control_active_.load())
    {
        LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] Rotation control thread running");

        auto current_time = std::chrono::steady_clock::now();
        float total_elapsed_time = std::chrono::duration<float>(current_time - rotation_start_time_).count();

        {
            std::lock_guard<std::mutex> lock(rotation_control_mutex_);
            rotation_result_.elapsed_time = total_elapsed_time;

            // 如果正在后退
            if (is_backing_up_)
            {
                float backup_elapsed = std::chrono::duration<float>(current_time - backup_start_time_).count();
                float backup_duration = param_.backup_distance / param_.backup_speed;

                if (backup_elapsed >= backup_duration)
                {
                    // 后退完成，停止后退，重新开始旋转
                    is_backing_up_ = false;
                    current_rotation_start_time_ = current_time;

                    // 停止后退运动
                    if (velocity_callback_)
                    {
                        velocity_callback_(0.0f, 0.0f, 100); // 停止100ms
                    }

                    LOG_INFO("[ImuDataProcessor] Backup completed, resuming rotation. Attempt: {}/{}",
                             backup_attempt_count_, param_.max_backup_attempts);
                }
                else
                {
                    // 继续后退
                    if (velocity_callback_)
                    {
                        velocity_callback_(-param_.backup_speed, 0.0f, 0); // 持续后退
                    }
                }
            }
            else
            {
                // 正常旋转模式
                float current_rotation_elapsed = std::chrono::duration<float>(current_time - current_rotation_start_time_).count();

                // 检查当前旋转是否超时
                if (current_rotation_elapsed >= param_.max_rotation_time)
                {
                    // 旋转超时，检查是否还能后退
                    if (backup_attempt_count_ < param_.max_backup_attempts)
                    {
                        // 开始后退
                        backup_attempt_count_++;
                        is_backing_up_ = true;
                        backup_start_time_ = current_time;

                        LOG_WARN("[ImuDataProcessor] Rotation timeout after {:.1f}s, starting backup attempt {}/{}",
                                 current_rotation_elapsed, backup_attempt_count_, param_.max_backup_attempts);

                        // 开始后退运动
                        if (velocity_callback_)
                        {
                            velocity_callback_(-param_.backup_speed, 0.0f, 0); // 开始后退
                        }
                    }
                    else
                    {
                        // 已达到最大后退次数，旋转失败
                        rotation_result_.timeout = true;
                        rotation_result_.completed = false;
                        LOG_ERROR("[ImuDataProcessor] Rotation failed after {} backup attempts, total time: {:.1f}s",
                                  param_.max_backup_attempts, total_elapsed_time);
                        break;
                    }
                }
                else
                {
                    // 检查旋转完成
                    float rotation_error = std::abs(target_rotation_angle_ - accumulated_rotation_);
                    rotation_result_.rotation_error = rotation_error;
                    rotation_result_.actual_rotation = accumulated_rotation_;

                    LOG_INFO_THROTTLE(500, "[ImuDataProcessor] Rotation status: target={:.3f}°, actual={:.3f}°, error={:.3f}°",
                                      target_rotation_angle_ * 180.0f / M_PI,
                                      accumulated_rotation_ * 180.0f / M_PI,
                                      rotation_error * 180.0f / M_PI);

                    if (rotation_error <= param_.rotation_tolerance)
                    {
                        rotation_result_.completed = true;
                        rotation_result_.timeout = false;
                        LOG_INFO("[ImuDataProcessor] Rotation completed: target={:.3f}°, actual={:.3f}°, error={:.3f}°",
                                 target_rotation_angle_ * 180.0f / M_PI,
                                 accumulated_rotation_ * 180.0f / M_PI,
                                 rotation_error * 180.0f / M_PI);
                        break;
                    }
                    else
                    {
                        // 继续旋转 - 发布旋转速度命令
                        float sign = target_rotation_angle_ >= 0.0f ? 1.0f : -1.0f;
                        if (velocity_callback_)
                        {
                            velocity_callback_(0.0f, sign * target_angular_velocity_, 0); // 持续旋转
                        }
                    }
                }
            }
        }

        // 控制频率 (50Hz)
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }

    // 确保停止所有运动
    if (velocity_callback_)
    {
        velocity_callback_(0.0f, 0.0f, 100);
    }

    rotation_control_active_.store(false);
    LOG_INFO("[ImuDataProcessor] Rotation control thread exited");
}

void ImuDataProcessor::LinearMotionControlThread()
{
    LOG_INFO("[ImuDataProcessor] Linear motion control thread started");

    const float distance_tolerance = 0.05f; // 5cm tolerance
    const float heading_tolerance = 0.087f; // 5 degrees tolerance
    const float max_motion_time = 300.0f;   // 300 seconds max
    // const float correction_gain = 0.3f;     // Angular velocity correction gain
    const float max_angular_vel = 0.5f; // Maximum angular velocity for correction

    while (linear_motion_control_active_.load())
    {
        LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] Linear motion control thread running");

        auto current_time = std::chrono::steady_clock::now();
        float total_elapsed_time = std::chrono::duration<float>(current_time - linear_motion_start_time_).count();

        {
            std::lock_guard<std::mutex> lock(linear_motion_control_mutex_);
            linear_motion_result_.elapsed_time = total_elapsed_time;

            // 检查超时
            if (total_elapsed_time >= max_motion_time)
            {
                linear_motion_result_.timeout = true;
                linear_motion_result_.completed = false;
                LOG_ERROR("[ImuDataProcessor] Linear motion control timeout after {:.1f}s", total_elapsed_time);
                break;
            }

            // 计算距离误差
            float target_distance_abs = std::abs(target_distance_);
            float distance_error = std::abs(target_distance_abs - accumulated_distance_);
            linear_motion_result_.distance_error = distance_error;
            linear_motion_result_.actual_distance = accumulated_distance_;

            // 计算航向误差 (左手坐标系处理)
            float current_heading = linear_motion_accumulated_rotation_;
            float heading_error = target_heading_ - current_heading;

            // 归一化航向误差到 [-π, π] (使用标准化函数)
            heading_error = NormalizeAngle(heading_error);

            linear_motion_result_.heading_error = heading_error;

            LOG_INFO_THROTTLE(100, "[ImuDataProcessor] Linear motion status: target_dist={:.3f}m, actual_dist={:.3f}m, dist_error={:.3f}m, heading_error={:.1f}°",
                              target_distance_abs, accumulated_distance_, distance_error, heading_error * 180.0f / M_PI);

            // 检查距离完成
            if (distance_error <= distance_tolerance)
            {
                linear_motion_result_.completed = true;
                linear_motion_result_.timeout = false;
                LOG_INFO("[ImuDataProcessor] Linear motion completed: target_dist={:.3f}m, actual_dist={:.3f}m, error={:.3f}m",
                         target_distance_abs, accumulated_distance_, distance_error);
                break;
            }
            else
            {
                // 继续运动 - 计算速度命令
                float expect_linear_velocity = expect_linear_velocity_;
                float expect_angular_velocity = 0.0f;

                // 航向纠正 (左手坐标系：正角速度为顺时针，负角速度为逆时针)
                if (IsTrajectoryDeviated(heading_error, heading_tolerance))
                {
                    expect_angular_velocity = CalculateTrajectoryCorrection(heading_error, max_angular_vel);

                    LOG_INFO_THROTTLE(100, "[ImuDataProcessor] Linear motion heading correction: error={:.1f}°, angular_vel={:.3f}rad/s",
                                      heading_error * 180.0f / M_PI, expect_angular_velocity);
                }

                // 发布速度命令
                if (velocity_callback_)
                {
                    velocity_callback_(expect_linear_velocity, expect_angular_velocity, 0); // 持续运动
                }
            }
        }

        // 控制频率 (50Hz)
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }

    // 确保停止所有运动
    if (velocity_callback_)
    {
        velocity_callback_(0.0f, 0.0f, 100);
    }

    linear_motion_control_active_.store(false);
    LOG_INFO("[ImuDataProcessor] Linear motion control thread exited");
}

void ImuDataProcessor::ContinuousLinearMotionThread()
{
    LOG_INFO("[ImuDataProcessor] Continuous linear motion control thread started");

    const float heading_tolerance = 0.087f; // 5 degrees tolerance
    // const float correction_gain = 0.3f;     // Angular velocity correction gain
    const float max_angular_vel = 0.5f; // Maximum angular velocity for correction

    while (continuous_linear_motion_active_.load())
    {
        LOG_INFO_THROTTLE(2000, "[ImuDataProcessor] Continuous linear motion control thread running");

        {
            std::lock_guard<std::mutex> lock(continuous_linear_motion_mutex_);

            // 计算航向误差 (左手坐标系处理)
            float current_heading = continuous_linear_motion_rotation_;
            float heading_error = continuous_target_heading_ - current_heading;

            // 归一化航向误差到 [-π, π] (使用标准化函数)
            heading_error = NormalizeAngle(heading_error);

            LOG_INFO_THROTTLE(100, "[ImuDataProcessor] Continuous motion status: target_heading={:.1f}°, current_heading={:.1f}°, heading_error={:.1f}°",
                              continuous_target_heading_ * 180.0f / M_PI, current_heading * 180.0f / M_PI, heading_error * 180.0f / M_PI);

            // 计算速度命令
            float expect_linear_velocity = continuous_expect_linear_velocity_;
            float expect_angular_velocity = 0.0f;

            // 航向纠正 (左手坐标系：正角速度为顺时针，负角速度为逆时针)
            if (IsTrajectoryDeviated(heading_error, heading_tolerance))
            {
                expect_angular_velocity = CalculateTrajectoryCorrection(heading_error, max_angular_vel);

                LOG_INFO_THROTTLE(100, "[ImuDataProcessor] Continuous heading correction: error={:.1f}°, angular_vel={:.3f}rad/s",
                                  heading_error * 180.0f / M_PI, expect_angular_velocity);
            }

            // 发布速度命令
            if (velocity_callback_)
            {
                velocity_callback_(expect_linear_velocity, expect_angular_velocity, 0); // 持续运动
            }
        }

        // 控制频率 (50Hz)
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }

    // 确保停止所有运动
    // if (velocity_callback_)
    // {
    //     velocity_callback_(0.0f, 0.0f, 100);
    // }

    continuous_linear_motion_active_.store(false);
    LOG_INFO("[ImuDataProcessor] Continuous linear motion control thread exited");
}

void ImuDataProcessor::UpdateRotationControl(float angular_velocity, float dt)
{
    if (!rotation_control_active_.load())
    {
        return;
    }

    std::lock_guard<std::mutex> lock(rotation_control_mutex_);

    // 累积旋转角度
    accumulated_rotation_ += angular_velocity * dt;

    LOG_INFO_THROTTLE(10, "[ImuDataProcessor][UpdateRotationControl1] Rotation update: angular_velocity={:.3f}rad/s", angular_velocity);

    LOG_INFO_THROTTLE(10, "[ImuDataProcessor][UpdateRotationControl1] Rotation update: accumulated={:.3f}°, target={:.3f}°",
                      accumulated_rotation_ * 180.0f / M_PI,
                      target_rotation_angle_ * 180.0f / M_PI);
}

void ImuDataProcessor::UpdateLinearMotionControl(float angular_velocity, float dt)
{
    if (!linear_motion_control_active_.load())
    {
        return;
    }

    std::lock_guard<std::mutex> lock(linear_motion_control_mutex_);

    // 计算距离增量 (考虑前进/后退方向)
    float distance_increment = expect_linear_velocity_ * dt;

    // 累积实际行驶距离 (始终为正值)
    accumulated_distance_ += std::abs(distance_increment);

    // 累积旋转角度 (左手坐标系：正角速度表示顺时针旋转)
    linear_motion_accumulated_rotation_ += angular_velocity * dt;

    // 归一化累积旋转角度
    linear_motion_accumulated_rotation_ = NormalizeAngle(linear_motion_accumulated_rotation_);

    LOG_INFO_THROTTLE(10, "[ImuDataProcessor][UpdateLinearMotionControl1] Linear motion update: accumulated_distance={:.3f}m, target_distance={:.3f}m, accumulated_rotation={:.3f}°, target_heading={:.3f}°",
                      accumulated_distance_, std::abs(target_distance_), linear_motion_accumulated_rotation_ * 180.0f / M_PI, target_heading_ * 180.0f / M_PI);
}

void ImuDataProcessor::UpdateContinuousLinearMotion(float angular_velocity, float dt)
{
    if (!continuous_linear_motion_active_.load())
    {
        return;
    }

    std::lock_guard<std::mutex> lock(continuous_linear_motion_mutex_);

    // 累积旋转角度 (左手坐标系：正角速度表示顺时针旋转)
    continuous_linear_motion_rotation_ += angular_velocity * dt;

    // 归一化累积旋转角度
    continuous_linear_motion_rotation_ = NormalizeAngle(continuous_linear_motion_rotation_);

    LOG_INFO_THROTTLE(10, "[ImuDataProcessor][UpdateContinuousLinearMotion] Continuous linear motion update: angular_velocity={:.3f}rad/s, rotation={:.3f}°",
                      angular_velocity, continuous_linear_motion_rotation_ * 180.0f / M_PI);
}

bool ImuDataProcessor::IsBiasCalibrated() const
{
    return is_bias_calibrated_;
}

float ImuDataProcessor::getCurrentYaw() const
{
    return current_yaw_;
}

float ImuDataProcessor::getBiasZ() const
{
    return bias_z_;
}

void ImuDataProcessor::SetParam(const ImuProcessorParam &param)
{
    param_ = param;
}

ImuProcessorParam ImuDataProcessor::GetParam() const
{
    return param_;
}

void ImuDataProcessor::ResetState()
{
    std::lock_guard<std::mutex> lock(imu_data_mutex_);

    // 重置校准状态
    // is_bias_calibrated_ = false;
    // filter_initialized_ = false;
    // bias_z_ = 0.0f;
    // bias_samples_.clear();
    // bias_samples_.reserve(param_.bias_calibration_samples);

    // 重置滤波状态
    // filtered_angular_velocity_ = 0.0f;

    // 重置时间状态
    // is_first_imu_ = true;
    last_imu_timestamp_ = 0;
    last_imu_time_ = std::chrono::steady_clock::now();

    // 重置当前状态
    current_yaw_ = 0.0f;

    // 重置位置估计
    ResetPositionEstimate();

    StopRotationControl();
    StopContinuousLinearMotion();
    StopLinearMotionControl();

    LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] State reset completed");
}

float ImuDataProcessor::NormalizeAngle(float angle)
{
    // 将角度归一化到 [-π, π] 范围 (适用于左手坐标系)
    while (angle > M_PI)
        angle -= 2.0f * M_PI;
    while (angle < -M_PI)
        angle += 2.0f * M_PI;
    return angle;
}

bool ImuDataProcessor::IsTrajectoryDeviated(float heading_error, float max_deviation)
{
    // 检查轨迹是否偏离超过阈值
    return std::abs(heading_error) > max_deviation;
}

float ImuDataProcessor::CalculateTrajectoryCorrection(float heading_error, float max_correction)
{
    // 计算轨迹纠正的角速度 (左手坐标系)
    // 如果heading_error > 0，需要逆时针旋转(负角速度)
    // 如果heading_error < 0，需要顺时针旋转(正角速度)
    float correction = heading_error * param_.correction_gain;

    // 限制纠正角速度的最大值
    correction = std::max(-max_correction, std::min(max_correction, correction));

    return correction;
}

void ImuDataProcessor::UpdatePositionEstimate(float linear_velocity, float angular_velocity, float dt)
{
    (void)angular_velocity;
    // 基于IMU的简单位置估计 (左手坐标系)
    // 使用当前航向角和线性速度进行积分

    // 计算位置增量 (左手坐标系：X轴向前，Y轴向右)
    float dx = linear_velocity * std::cos(current_yaw_) * dt;
    float dy = linear_velocity * std::sin(current_yaw_) * dt;

    // 更新位置估计
    estimated_x_ += dx;
    estimated_y_ += dy;

    LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] Position estimate: x={:.3f}m, y={:.3f}m, yaw={:.1f}°",
                      estimated_x_, estimated_y_, current_yaw_ * 180.0f / M_PI);
}

void ImuDataProcessor::ResetPositionEstimate()
{
    estimated_x_ = 0.0f;
    estimated_y_ = 0.0f;
    LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] Position estimate reset");
}

float ImuDataProcessor::getEstimatedX() const
{
    return estimated_x_;
}

float ImuDataProcessor::getEstimatedY() const
{
    return estimated_y_;
}

void ImuDataProcessor::InitializeDataLogging()
{
    try
    {
        log_file_.open(param_.log_file_path, std::ios::out | std::ios::trunc);
        if (log_file_.is_open())
        {
            // 写入CSV头部
            log_file_ << "timestamp,raw_angular_velocity,filtered_angular_velocity\n";
            logging_initialized_ = true;
            LOG_INFO("[ImuDataProcessor] Data logging initialized: {}", param_.log_file_path);
        }
        else
        {
            LOG_ERROR("[ImuDataProcessor] Failed to open log file: {}", param_.log_file_path);
        }
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("[ImuDataProcessor] Exception in data logging initialization: {}", e.what());
    }
}

void ImuDataProcessor::LogFilteringData(uint64_t timestamp, float raw_angular_velocity, float filtered_angular_velocity)
{
    if (!logging_initialized_ || !log_file_.is_open())
    {
        return;
    }

    try
    {
        log_file_ << timestamp << ","
                  << std::fixed << std::setprecision(6)
                  << raw_angular_velocity << ","
                  << filtered_angular_velocity << "\n";
        log_file_.flush();
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("[ImuDataProcessor] Exception in data logging: {}", e.what());
    }
}

void ImuDataProcessor::CloseDataLogging()
{
    if (logging_initialized_ && log_file_.is_open())
    {
        log_file_.close();
        logging_initialized_ = false;
        LOG_INFO("[ImuDataProcessor] Data logging closed");
    }
}

} // namespace fescue_iox
