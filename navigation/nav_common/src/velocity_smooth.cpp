#include "velocity_smooth.hpp"

#include "mower_sdk_version.h"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "velocity_smooth_config.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

NavigationVelocitySmoothAlg::NavigationVelocitySmoothAlg(const VelocitySmoothAlgParam &param)
{
    SetVelocitySmoothParam(param);
}

NavigationVelocitySmoothAlg::~NavigationVelocitySmoothAlg()
{
}

VelocitySmoothAlgResult NavigationVelocitySmoothAlg::DoVelocitySmooth(const VelocitySmoothAlgInput &input)
{
    VelocitySmoothAlgResult output;

    if (!ValidateTwist(input) ||
        last_command_time_ == 0 ||
        (input.timestamp_ms - last_command_time_ > velocity_timeout_ * 1000))
    {
        LOG_ERROR("Velocity message contains NaNs or Infs! Ignoring as invalid!");
        last_command_time_ = input.timestamp_ms;
        output.linear = input.linear;
        output.angular = input.angular;
        return output;
    }

    last_command_time_ = input.timestamp_ms;
    command_.linear = input.linear;
    command_.angular = input.angular;
    stopped_ = false;

    // Get current velocity based on feedback type // 根据反馈类型获取当前速度
    VelocitySmoothAlgInput current_;
    if (open_loop_)
    {
        current_ = last_cmd_;
    }
    else
    {
        // current_ = odom_smoother_->getTwist();
    }

    // Apply absolute velocity restrictions to the command  // 对命令应用绝对速度限制
    command_.linear.x = std::clamp(command_.linear.x, min_velocities_[0], max_velocities_[0]);
    command_.linear.y = std::clamp(command_.linear.y, min_velocities_[1], max_velocities_[1]);
    command_.angular.z = std::clamp(command_.angular.z, min_velocities_[2], max_velocities_[2]);

    // 查找是否有任何分量不在加速度约束范围内。如果是，则存储最显著的缩放因子
    // 以便将矢量 <dvx, dvy, dvw> 按比例缩小，eta，以在速度变化范围内沿相同方向减小所有轴。
    // 如果 eta 使另一个轴超出其自身限制，则应用加速度约束以确保输出在限制范围内，
    // 即使它略微偏离请求的命令。
    double eta = 1.0;
    if (scale_velocities_)
    {
        double curr_eta = -1.0;
        curr_eta = FindEtaConstraint(current_.linear.x, command_.linear.x, max_accels_[0], max_decels_[0]);
        if (curr_eta > 0.0 && std::fabs(1.0 - curr_eta) > std::fabs(1.0 - eta))
        {
            eta = curr_eta;
        }

        curr_eta = FindEtaConstraint(current_.linear.y, command_.linear.y, max_accels_[1], max_decels_[1]);
        if (curr_eta > 0.0 && std::fabs(1.0 - curr_eta) > std::fabs(1.0 - eta))
        {
            eta = curr_eta;
        }

        curr_eta = FindEtaConstraint(current_.angular.z, command_.angular.z, max_accels_[2], max_decels_[2]);
        if (curr_eta > 0.0 && std::fabs(1.0 - curr_eta) > std::fabs(1.0 - eta))
        {
            eta = curr_eta;
        }
    }

    output.linear.x = ApplyConstraints(current_.linear.x, command_.linear.x, max_accels_[0], max_decels_[0], eta);
    output.linear.y = ApplyConstraints(current_.linear.y, command_.linear.y, max_accels_[1], max_decels_[1], eta);
    output.angular.z = ApplyConstraints(current_.angular.z, command_.angular.z, max_accels_[2], max_decels_[2], eta);

    last_cmd_.linear = output.linear;
    last_cmd_.angular = output.angular;

    // Apply deadband restrictions & publish  // 应用死区限制并发布命令
    output.linear.x = fabs(output.linear.x) < deadband_velocities_[0] ? 0.0 : output.linear.x;
    output.linear.y = fabs(output.linear.y) < deadband_velocities_[1] ? 0.0 : output.linear.y;
    output.angular.z = fabs(output.angular.z) < deadband_velocities_[2] ? 0.0 : output.angular.z;

    LOG_DEBUG_THROTTLE(500,"cmd_vel_smoothed->linear.x: {}, cmd_vel_smoothed->linear.y: {},  cmd_vel_smoothed->angular.z: {}",
              output.linear.x, output.linear.y, output.angular.z);

    return output;
}

int NavigationVelocitySmoothAlg::SetVelocitySmoothParam(const VelocitySmoothAlgParam &config)
{
    open_loop_ = config.open_loop;
    scale_velocities_ = config.scale_velocities;
    velocity_timeout_ = config.velocity_timeout;
    smoothing_frequency_ = config.smoothing_frequency;

    max_velocities_ = config.max_velocities;
    min_velocities_ = config.min_velocities;
    max_accels_ = config.max_accels;
    max_decels_ = config.max_decels;
    deadband_velocities_ = config.deadband_velocities;

    bool is_valid_max_decels = true;
    bool is_valid_max_accels = true;
    bool is_valid_velocities = true;

    for (unsigned int i = 0; i != 3; i++)
    {
        if (max_decels_[i] > 0.0)
        {
            LOG_ERROR("Positive values set of deceleration! These should be negative to slow down!");
            is_valid_max_decels = false;
        }
        if (max_accels_[i] < 0.0)
        {
            LOG_ERROR("Negative values set of acceleration! These should be positive to speed up!");
            is_valid_max_accels = false;
        }
        if (min_velocities_[i] > max_velocities_[i])
        {
            LOG_ERROR("Min velocities are higher than max velocities!!");
            is_valid_velocities = false;
        }
    }

    if (!is_valid_max_decels)
    {
        max_accels_ = {5.0, 0.0, 2.5};
    }

    if (!is_valid_max_accels)
    {
        max_accels_ = {5.0, 0.0, 2.5};
    }
    if (!is_valid_velocities)
    {
        max_velocities_ = {0.3, 0.0, 0.6};
        min_velocities_ = {-0.3, 0.0, -0.6};
    }

    if (max_velocities_.size() != 3 || min_velocities_.size() != 3 ||
        max_accels_.size() != 3 || max_decels_.size() != 3 || deadband_velocities_.size() != 3)
    {
        LOG_ERROR("Invalid setting of kinematic and/or deadband limits!"
                  " All limits must be size of 3 representing (x, y, theta).");
        max_velocities_ = {0.3, 0.0, 0.6};
        min_velocities_ = {-0.3, 0.0, -0.6};
        max_accels_ = {5.0, 0.0, 2.5};
        max_decels_ = {-5.0, 0.0, -2.5};
        deadband_velocities_ = {0.01, 0.01, 0.01};
    }
    return 0;
}

bool NavigationVelocitySmoothAlg::ValidateTwist(const VelocitySmoothAlgInput &msg)
{
    // 检查线速度分量 x 是否包含 NaN 或 Inf
    if (std::isinf(msg.linear.x) || std::isnan(msg.linear.x))
    {
        return false;
    }

    if (std::isinf(msg.linear.y) || std::isnan(msg.linear.y))
    {
        return false;
    }

    if (std::isinf(msg.linear.z) || std::isnan(msg.linear.z))
    {
        return false;
    }

    if (std::isinf(msg.angular.x) || std::isnan(msg.angular.x))
    {
        return false;
    }

    if (std::isinf(msg.angular.y) || std::isnan(msg.angular.y))
    {
        return false;
    }

    if (std::isinf(msg.angular.z) || std::isnan(msg.angular.z))
    {
        return false;
    }

    return true;
}

double NavigationVelocitySmoothAlg::FindEtaConstraint(const double v_curr, const double v_cmd, const double accel, const double decel)
{
    // Exploiting vector scaling properties // 利用向量缩放性质
    double dv = v_cmd - v_curr;

    double v_component_max; // 最大速度分量
    double v_component_min;

    // 如果指令速度的大小大于当前速度的大小，并且指令速度和当前速度的符号相同（即速度不会通过0.0），则加速
    // 否则减速
    if (fabs(v_cmd) >= fabs(v_curr) && v_curr * v_cmd >= 0.0)
    {
        // 计算加速度下的最大和最小速度分量
        v_component_max = accel / smoothing_frequency_;
        v_component_min = -accel / smoothing_frequency_;
    }
    else
    {
        // 计算减速度下的最大和最小速度分量
        v_component_max = -decel / smoothing_frequency_;
        v_component_min = decel / smoothing_frequency_;
    }

    // 如果速度差大于最大速度分量，则返回缩放因子
    if (dv > v_component_max)
    {
        return v_component_max / dv;
    }

    // 如果速度差小于最小速度分量，则返回缩放因子
    if (dv < v_component_min)
    {
        return v_component_min / dv;
    }

    return -1.0; // 否则返回 -1.0 表示无需缩放
}

double NavigationVelocitySmoothAlg::ApplyConstraints(const double v_curr, const double v_cmd,
                                                     const double accel, const double decel, const double eta)
{
    double dv = v_cmd - v_curr;

    double v_component_max; // 最大速度分量
    double v_component_min;

    // 如果指令速度的大小大于当前速度的大小，并且指令速度和当前速度的符号相同（即速度不会通过0.0），则加速
    // 否则减速
    if (fabs(v_cmd) >= fabs(v_curr) && v_curr * v_cmd >= 0.0)
    {
        v_component_max = accel / smoothing_frequency_;
        v_component_min = -accel / smoothing_frequency_;
    }
    else
    {
        v_component_max = -decel / smoothing_frequency_;
        v_component_min = decel / smoothing_frequency_;
    }

    return v_curr + std::clamp(eta * dv, v_component_min, v_component_max);
}

} // namespace fescue_iox
