#ifndef NAVIGATION_PREDICT_TRAJECTORY_HPP
#define NAVIGATION_PREDICT_TRAJECTORY_HPP

#include "opencv2/opencv.hpp"
#include "predict_trajectory_config.hpp"
#include "utils/logger.hpp"
#include "utils/math_type.hpp"

#include <chrono>
#include <cmath>
#include <deque>
#include <fstream>
#include <functional>
#include <iostream>
#include <limits>
#include <memory>
#include <queue>
#include <set>
#include <string>
#include <vector>

using namespace std;

namespace fescue_iox
{

struct pixel
{
    int x;
    int y;
};

struct Twist2D
{
    double x;
    double theta;
};

struct Pose
{
    double x;
    double y;
    double theta;
    Pose() = default;
    Pose(double x, double y, double theta)
        : x(x)
        , y(y)
        , theta(theta)
    {
    }
};

struct Compare
{
    bool operator()(const Pose &lhs, const Pose &rhs) const
    {
        return lhs.x < rhs.x;
        // return lhs.y < rhs.y;
    }
};

struct Compare_x
{
    bool operator()(const Pose &lhs, const Pose &rhs) const
    {
        return lhs.x < rhs.x;
    }
};
struct Compare_y
{
    bool operator()(const Pose &lhs, const Pose &rhs) const
    {
        return lhs.y < rhs.y;
    }
};

struct Compare_d
{
    bool operator()(const Pose &lhs, const Pose &rhs) const
    {
        return hypot(lhs.x, lhs.y) < hypot(rhs.x, rhs.y);
    }
};

struct PredictTrajectoryAlgParam
{
    double twirling_scale{10.0};
    double perfer_forward_scale{1.0};
    double path_follow_scale{1.0};
    double path_angle_scale{10.0};
    double expand_dis{0.35}; // 0.25
    double expand_dis_check{0.001};
    int min_counter_size{2}; // 10
    int look_ahead_dis{220};

    double positive_sdf_gain{10.0};
    double negative_sdf_gain{1.0};
    double max_yaw_speed{0.6};
    double predict_time{3.0}; // 3.0
    double d_prox{0.25};
    double v_max{0.2};
    double alpha{1.0};
    double beta{1.0};
    int find_target{1};
};

struct PredictTrajectoryAlgResult
{
    float angular{0};
    float linear{0};
    PredictTrajectoryAlgResult() = default;
    PredictTrajectoryAlgResult(float linear, float angular)
        : angular(angular)
        , linear(linear)
    {
    }
};

class PredictTrajectory
{
public:
    PredictTrajectory(double twirling_scale, double perfer_forward_scale, double path_follow_scale,
                      double path_angle_scale, double expand_dis, double expand_dis_check,
                      int min_counter_size, int look_ahead_dis, double positive_sdf_gain, double negative_sdf_gain,
                      double max_yaw_speed, double predict_time, double d_prox, double v_max, double alpha, double beta, double find_target)
    {
        grid_val_.resize(360, vector<double>(400));
        grid_res_.resize(360, vector<double>(400));
        grid_val_neg_.resize(360, vector<double>(400));
        grid_res_neg_.resize(360, vector<double>(400));
        grid_res_all_.resize(360, vector<double>(400));
        field_obs_.resize(360, vector<double>(400));
        twirling_scale_ = twirling_scale;
        perfer_forward_scale_ = perfer_forward_scale;
        path_follow_scale_ = path_follow_scale;
        path_angle_scale_ = path_angle_scale;
        expand_dis_ = expand_dis;
        expand_dis_check_ = expand_dis_check;
        min_counter_size_ = min_counter_size;
        look_ahead_dis_ = look_ahead_dis;
        positive_sdf_gain_ = positive_sdf_gain;
        negative_sdf_gain_ = negative_sdf_gain;
        max_yaw_speed_ = max_yaw_speed;
        predict_time_ = predict_time;
        d_prox_ = d_prox;
        v_max_ = v_max;
        alpha_ = alpha;
        beta_ = beta;
        find_target_ = find_target;
        // std::cout << "---------PredictTrajectory init---------------" << std::endl;
        LOG_DEBUG("---------PredictTrajectory init---------------");
    }

    PredictTrajectory(const PredictTrajectoryAlgParam &param)
    {
        grid_val_.resize(360, std::vector<double>(400));
        grid_res_.resize(360, std::vector<double>(400));
        grid_val_neg_.resize(360, vector<double>(400));
        grid_res_neg_.resize(360, vector<double>(400));
        grid_res_all_.resize(360, vector<double>(400));
        field_obs_.resize(360, vector<double>(400));
        SetParam(param);
        // std::cout << "---------PredictTrajectory init---------------" << std::endl;
        LOG_DEBUG("---------PredictTrajectory init---------------");
    }

    void SetParam(const PredictTrajectoryAlgParam &param)
    {
        twirling_scale_ = param.twirling_scale;
        perfer_forward_scale_ = param.perfer_forward_scale;
        path_follow_scale_ = param.path_follow_scale;
        path_angle_scale_ = param.path_angle_scale;
        expand_dis_ = param.expand_dis;
        expand_dis_check_ = param.expand_dis_check;
        min_counter_size_ = param.min_counter_size;
        look_ahead_dis_ = param.look_ahead_dis;
        positive_sdf_gain_ = param.positive_sdf_gain;
        negative_sdf_gain_ = param.negative_sdf_gain;
        max_yaw_speed_ = param.max_yaw_speed;
        predict_time_ = param.predict_time;
        d_prox_ = param.d_prox;
        v_max_ = param.v_max;
        alpha_ = param.alpha;
        beta_ = param.beta;
        find_target_ = param.find_target;
    }

    template <typename F_get_val, typename F_set_val>
    void fillESDF(F_get_val f_get_val, F_set_val f_set_val, int start, int end, int dim)
    {
        // int v[mp_.map_voxel_num_(dim)];
        // double z[mp_.map_voxel_num_(dim) + 1];
        std::vector<int> v(dim);
        std::vector<double> z(dim + 1);
        int k = start;
        v[start] = start;
        z[start] = -std::numeric_limits<double>::max();
        z[start + 1] = std::numeric_limits<double>::max();

        for (int q = start + 1; q <= end; q++)
        {
            k++;
            double s;

            do
            {
                k--;
                s = ((f_get_val(q) + q * q) - (f_get_val(v[k]) + v[k] * v[k])) / (2 * q - 2 * v[k]);
            }
            while (s <= z[k]);

            k++;

            v[k] = q;
            z[k] = s;
            z[k + 1] = std::numeric_limits<double>::max();
        }

        k = start;

        for (int q = start; q <= end; q++)
        {
            while (z[k + 1] < q)
                k++;
            double val = (q - v[k]) * (q - v[k]) + f_get_val(v[k]);
            f_set_val(q, val);
        }
    }
    std::vector<int> CheckSafeAndGenerateDm(cv::Mat img, const std::vector<std::vector<Pose>> &TrajectoryList)
    {
        auto start = std::chrono::high_resolution_clock::now();
        std::vector<int> free_idx;
        int cols = img.cols;
        int rows = img.rows;
        // double grid_res_[rows][cols];
        std::cout << "GenerateDm GET a img :" << cols << "," << rows << std::endl;
        /*EDT*/
        for (int x = 0; x < cols; x++)
        {
            fillESDF([&](int y) { return img.at<uint8_t>(y, x) > 128 ? 0 : std::numeric_limits<double>::max(); }, [&](int y, double val) { grid_val_[y][x] = val; }, 0, rows - 1, rows);
        }
        for (int y = 0; y < rows; y++)
        {
            fillESDF([&](int x) { return grid_val_[y][x]; }, [&](int x, double val) { grid_res_[y][x] = std::sqrt(val); }, 0, cols - 1, cols);
        }

        cv::Mat dm_img_EDT = img.clone();
        double max_val = -std::numeric_limits<double>::max();
        for (int x = 0; x < cols; x++)
        {
            for (int y = 0; y < rows; y++)
            {
                if (max_val < grid_res_[y][x])
                {
                    max_val = grid_res_[y][x];
                }
            }
        }
        for (int x = 0; x < cols; x++)
        {
            for (int y = 0; y < rows; y++)
            {
                dm_img_EDT.at<uint8_t>(y, x) = 255 - grid_res_[y][x] / max_val * 255;
            }
        }

        cv::Mat thresh1;
        // TODO: safe_dis决定了与边界的距离:0.25
        int safe_dis = 255 - expand_dis_ / resolution_ / max_val * 255;
        cv::threshold(dm_img_EDT, thresh1, safe_dis, 255, cv::THRESH_BINARY);
        int safe_dis_bin = 255 - expand_dis_check_ / resolution_ / max_val * 255;
        cv::threshold(dm_img_EDT, Bin_map_, safe_dis_bin, 255, cv::THRESH_BINARY);
        auto end = std::chrono::high_resolution_clock::now();
        std::chrono::duration<double> duration = end - start;
        std::cout << "dm cost " << duration.count() << "seconds" << std::endl;
        /*此处可以检查是否有轨迹可行，如果没有直接返回空的候选轨迹*/
        free_idx = FindFreeTraj(TrajectoryList, img);
        if (!free_idx.empty())
        {
            std::vector<std::vector<cv::Point>> contours1;
            std::vector<cv::Vec4i> hierarchy1;
            cv::findContours(thresh1, contours1, hierarchy1, cv::RETR_CCOMP, cv::CHAIN_APPROX_SIMPLE);

            std::vector<std::vector<cv::Point>> contours2;
            // TODO：Is there a better way to filter reference lines?
            for (size_t i = 0; i < contours1.size(); i++)
            {
                std::vector<cv::Point> sub_contour;
                for (size_t j = 0; j < contours1[i].size(); j++)
                {
                    if (contours1[i][j].x > 15 &&
                        contours1[i][j].x < cols - 16 &&
                        contours1[i][j].y > look_ahead_dis_ &&
                        contours1[i][j].y < rows - 2)
                    {
                        cv::Point p(contours1[i][j].x, contours1[i][j].y);
                        sub_contour.push_back(p);
                    }
                }
                std::cout << "contours1 size size :" << contours1[i].size() << std::endl;
                std::cout << "sub_contour size:" << sub_contour.size() << std::endl;
                if ((int)sub_contour.size() >= min_counter_size_)
                {
                    contours2.push_back(sub_contour);
                }
            }
            // TODO：Handling the situation where the number of contours2 is greater than 2.
            // TODO: How to deal with the full image is obstacle？
            if (contours2.size() == 1)
            {
                Path_edt_ = contours2[0];
                std::cout << "copy contours2" << std::endl;
                // how about just consider a short version?
            }
            else if (contours2.size() == 2)
            {
                std::vector<std::vector<cv::Point>> contours3(1);
                /*由于contours的不确定性即可能是封闭也可能是非封闭，该方法生成的参考轨迹不稳定*/
                /*
                for (int  i = 0; i < contours2[0].size(); i++)
                {
                    double min_dis = std::numeric_limits<double>::max();
                    int idx = -1;
                    for (int  j = 0; j < contours2[1].size(); j++)
                    {
                        int dx = contours2[0][i].x - contours2[1][j].x;
                        int dy = contours2[0][i].y - contours2[1][j].y;
                        double s = std::sqrt(dx*dx+dy * dy);
                        if (s<min_dis)
                        {
                            min_dis = s;
                            idx = j;
                        }

                    }
                    if (idx < contours2[1].size()-1)
                    {
                        cv::Point p((contours2[0][i].x + contours2[1][idx].x)/2,((contours2[0][i].y + contours2[1][idx].y)/2));
                        contours3[0].push_back(p);
                    }
                }
                */
                for (int i = 220; i < rows_; i++)
                {
                    double maxValue = -std::numeric_limits<double>::max();
                    cv::Point maxIndex;
                    for (int j = 0; j < cols; j++)
                    {
                        if (grid_res_[i][j] > maxValue)
                        {
                            maxIndex.x = j;
                            maxIndex.y = i;
                            maxValue = grid_res_[i][j];
                        }
                    }
                    contours3[0].emplace_back(maxIndex);
                }

                Path_edt_ = contours3[0];
                std::cout << "copy contours3" << std::endl;
            }
            // TODO：如果contour1为空则Path_edt_也为空，应该对这种情况进行处理！！！
            ref_path_ = GetRefPath(Path_edt_);
            if (!ref_path_.empty())
            {
                get_ref_path_ = true;
            }
            // auto end = std::chrono::high_resolution_clock::now();
            // std::chrono::duration<double> duration = end - start;
            // std::cout<<"EDT dm cost time"<<duration.count()<<"seconds"<<std::endl;
            std::cout << "Contours1 size:" << contours1.size() << std::endl;
            std::cout << "Contours2 size:" << contours2.size() << std::endl;
            std::cout << "d_o: " << grid_res_[robot_in_img_y_][robot_in_img_x_] * resolution_ << std::endl;
        }
        return free_idx;
    }

    std::pair<double, double> GenerateConstVelCtr(cv::Mat img, double v)
    {
        std::pair<double, double> ctrl = std::make_pair(0, 0);
        auto start = std::chrono::high_resolution_clock::now();
        int cols = img.cols;
        int rows = img.rows;
        cv::Mat invertedImage;
        cv::bitwise_not(img, invertedImage);
        std::cout << "GenerateSDF GET a img :" << cols << "," << rows << std::endl;

        for (int x = 0; x < cols; x++)
        {
            fillESDF([&](int y) { return img.at<uint8_t>(y, x) > 128 ? 0 : std::numeric_limits<double>::max(); }, [&](int y, double val) { grid_val_[y][x] = val; }, 0, rows - 1, rows);

            fillESDF([&](int y) { return invertedImage.at<uint8_t>(y, x) > 128 ? 0 : std::numeric_limits<double>::max(); }, [&](int y, double val) { grid_val_neg_[y][x] = val; }, 0, rows - 1, rows);
        }
        for (int y = 0; y < rows; y++)
        {
            fillESDF([&](int x) { return grid_val_[y][x]; }, [&](int x, double val) { grid_res_[y][x] = std::sqrt(val); }, 0, cols - 1, cols);

            fillESDF([&](int x) { return grid_val_neg_[y][x]; }, [&](int x, double val) { grid_res_neg_[y][x] = std::sqrt(val); }, 0, cols - 1, cols);
        }
        /*combine*/
        double k = positive_sdf_gain_;
        double r = expand_dis_;
        std::vector<std::pair<int, int>> valley_bottom;
        for (int x = 0; x < cols; x++)
        {
            for (int y = 0; y < rows; y++)
            {
                grid_res_all_[y][x] = grid_res_[y][x];
                if (grid_res_neg_[y][x] > 0)
                {
                    grid_res_all_[y][x] += -grid_res_neg_[y][x];
                }
                grid_res_all_[y][x] *= resolution_;
                field_obs_[y][x] = k * std::pow(grid_res_all_[y][x] - r, 2);

                if (field_obs_[y][x] == 0)
                {
                    valley_bottom.push_back(make_pair(y, x));
                }
            }
        }
        std::cout << "valley_bottom" << valley_bottom.size() << std::endl;
        /*check whether have traveable area,basically travelable_ is only use in narrow passage case*/
        if (valley_bottom.size() > travelable_num_)
        {
            travelable_ = true;
        }
        else
        {
            travelable_ = false;
        }
        auto end = std::chrono::high_resolution_clock::now();
        std::chrono::duration<double> duration = end - start;
        std::cout << "sdf cost " << duration.count() << "seconds" << std::endl;
        /*trajectory is in img frame*/
        Pose robot_pose_map(robot_in_map_x_ * resolution_, robot_in_map_y_ * resolution_, robot_in_map_yaw_ * 1.0);
        std::vector<std::vector<Pose>> TrajectoryList;
        std::vector<double> yaw_speed_vector;
        std::vector<int> idx_vector;
        for (double yaw_speed = -max_yaw_speed_; yaw_speed <= max_yaw_speed_; yaw_speed += M_PI / 18)
        {
            yaw_speed_vector.push_back(yaw_speed);
        }
        yaw_speed_vector.push_back(0.0);
        /*should add yaw speed == 0*/
        for (size_t i = 0; i < yaw_speed_vector.size(); i++)
        {
            double yaw_speed = yaw_speed_vector.at(i);
            bool collision_free = true;
            double t = 0.0;
            Pose cs{0, 0, 0};
            std::vector<Pose> Trajectory;
            while (t <= predict_time_)
            {
                Pose nstate = Pose{0, 0, 0};
                double nyaw = cs.theta + yaw_speed * dt_;
                double nx = cs.x + v * cos(nyaw) * dt_;
                double ny = cs.y + v * sin(nyaw) * dt_;
                nstate.x = nx;
                nstate.y = ny;
                nstate.theta = nyaw;
                cs = nstate;
                Pose pose_img = L2I(robot_pose_map, cs);
                int p_car_x = std::ceil(pose_img.x / resolution_);
                int p_car_y = std::ceil(pose_img.y / resolution_);
                if (0 < p_car_x && p_car_x < cols && 0 < p_car_y && p_car_y < rows)
                {
                    if (img.at<uint8_t>(p_car_y, p_car_x) == 255)
                    {
                        collision_free = false;
                        break;
                    }
                }
                Trajectory.push_back(pose_img);
                t += dt_;
            }
            if (collision_free)
            {
                TrajectoryList.push_back(Trajectory);
                idx_vector.push_back(i);
            }
        }
        auto end2 = std::chrono::high_resolution_clock::now();
        std::chrono::duration<double> duration2 = end2 - end;
        std::cout << "trajectory cost " << duration2.count() << "seconds" << std::endl;

        std::cout << "travelable:" << travelable_ << std::endl;
        std::cout << "TrajectoryList sieze:" << TrajectoryList.size() << std::endl;
        std::cout << "yaw_speed_vector sieze:" << yaw_speed_vector.size() << std::endl;
        std::vector<double> score_vector;
        double minScore = std::numeric_limits<double>::max(); // 最低分
        std::vector<int> minIndices;                          // 存储最低分轨迹的索引
        if (travelable_ && TrajectoryList.size() > 0)
        {
            for (size_t i = 0; i < TrajectoryList.size(); i++)
            {
                double score = 0;
                int idx = idx_vector.at(i);
                for (size_t j = 0; j < TrajectoryList.at(i).size(); j++)
                {
                    int x = std::ceil(TrajectoryList[i][j].x / resolution_);
                    int y = std::ceil(TrajectoryList[i][j].y / resolution_);
                    score += field_obs_[y][x];
                }
                score_vector.push_back(score);
                if (score < minScore)
                {
                    minScore = score;
                    minIndices.clear();
                    minIndices.push_back(idx); // 更新最低分索引
                }
                else if (score == minScore)
                {
                    minIndices.push_back(idx); // 添加相同最低分的索引
                }
                std::cout << "score:" << score << std::endl;
            }
            if (minIndices.size() == 1)
            {
                int idx = minIndices.at(0);
                ctrl.second = yaw_speed_vector.at(idx);
                std::cout << "only one miniIndices,score:" << score_vector.at(idx) << "idx:" << idx << " yaw:" << ctrl.second << std::endl;
            }
            else if (minIndices.size() > 1)
            {
                int min_yaw_idx;
                double min_yaw = std::numeric_limits<double>::max();
                for (auto k : minIndices)
                {
                    if (min_yaw > fabs(yaw_speed_vector.at(k)))
                    {
                        min_yaw = fabs(yaw_speed_vector.at(k));
                        min_yaw_idx = k;
                    }
                }
                ctrl.second = yaw_speed_vector.at(min_yaw_idx);
                std::cout << "multipule miniIndices,score:" << score_vector.at(min_yaw_idx) << "idx:" << min_yaw_idx << " yaw:" << ctrl.second << std::endl;
            }
            ctrl.first = v;
        }
        auto end3 = std::chrono::high_resolution_clock::now();
        std::chrono::duration<double> duration3 = end3 - start;
        std::cout << "tolly cost " << duration3.count() << "seconds" << std::endl;
        return ctrl;
    }

    std::pair<double, double> GenerateAdpVelCtr(cv::Mat img)
    {
        std::pair<double, double> ctrl = std::make_pair(0, 0);
        auto start = std::chrono::high_resolution_clock::now();
        int cols = img.cols;
        int rows = img.rows;
        // cv::Mat invertedImage;
        // cv::bitwise_not(img,invertedImage);
        LOG_DEBUG("GenerateSDF GET a img {},  {}", cols, rows);
        // std::cout << "GenerateSDF GET a img :" << cols << "," << rows << std::endl;

        for (int x = 0; x < cols; x++)
        {
            fillESDF([&](int y) { return img.at<uint8_t>(y, x) > 128 ? 0 : std::numeric_limits<double>::max(); }, [&](int y, double val) { grid_val_[y][x] = val; }, 0, rows - 1, rows);

            // fillESDF([&](int y){return invertedImage.at<uint8_t>(y,x)> 128 ?
            //     0: std::numeric_limits<double>::max();} ,[&](int y,double val){grid_val_neg_[y][x] = val;}  , 0, rows-1, rows);
        }
        double max_val = std::numeric_limits<double>::min();
        for (int y = 0; y < rows; y++)
        {
            fillESDF([&](int x) { return grid_val_[y][x]; }, [&](int x, double val) {grid_res_[y][x] = std::sqrt(val);if (std::sqrt(val) > max_val)
            {
                max_val = std::sqrt(val);
            } }, 0, cols - 1, cols);

            // fillESDF([&](int x){return grid_val_neg_[y][x];} ,[&](int x,double val){grid_res_neg_[y][x] = std::sqrt(val);}  , 0, cols-1, cols);
        }
        auto end = std::chrono::high_resolution_clock::now();
        std::chrono::duration<double> duration = end - start;
        // std::cout << "esdf cost " << duration.count() << " s" << std::endl;
        LOG_DEBUG("esdf cost: {} s", duration.count());

        // double d_O = grid_res_[robot_in_img_y_][robot_in_img_x_] * resolution_;
        // /*adaptive ctrl_v*/
        double ctrl_v = 0;
        // if (d_O > d_prox_)
        // {
        //     ctrl_v = v_max_;
        // }
        // else
        // {
        //     ctrl_v = v_max_ * alpha_ * d_O / d_prox_;
        // }

        ctrl_v = v_max_;

        // std::cout << "generate ctrl_v:" << ctrl_v << std::endl;
        LOG_DEBUG("generate ctrl_v:", ctrl_v);
        Pose robot_pose_map(robot_in_map_x_ * resolution_, robot_in_map_y_ * resolution_, robot_in_map_yaw_ * 1.0);
        std::vector<std::vector<Pose>> TrajectoryList;
        std::vector<double> yaw_speed_vector;
        std::vector<int> idx_vector;
        for (double yaw_speed = -max_yaw_speed_; yaw_speed <= max_yaw_speed_; yaw_speed += M_PI / 60)
        {
            yaw_speed_vector.push_back(yaw_speed);
        }
        yaw_speed_vector.push_back(0.0);

        // std::cout << "generate yaw_speed_vector" << std::endl;
        LOG_DEBUG("generate yaw_speed_vector");
        /*Trajectory is in local frame*/
        for (int k = 0; k < 3; k++)
        {
            LOG_DEBUG("predict {} second", k + 1);
            for (size_t i = 0; i < yaw_speed_vector.size(); i++)
            {
                double yaw_speed = yaw_speed_vector.at(i);
                bool collision_free = true;
                double t = 0.0;
                Pose cs{0, 0, 0};
                std::vector<Pose> Trajectory;
                while (t <= predict_time_ - double(k))
                {
                    Pose nstate = Pose{0, 0, 0};
                    double nyaw = cs.theta + yaw_speed * dt_;
                    double nx = cs.x + ctrl_v * cos(nyaw) * dt_;
                    double ny = cs.y + ctrl_v * sin(nyaw) * dt_;
                    nstate.x = nx;
                    nstate.y = ny;
                    nstate.theta = nyaw;
                    cs = nstate;
                    Pose pose_img = L2I(robot_pose_map, cs);
                    int p_car_x = std::ceil(pose_img.x / resolution_);
                    int p_car_y = std::ceil(pose_img.y / resolution_);
                    if (0 < p_car_x && p_car_x < cols && 0 < p_car_y && p_car_y < rows)
                    {
                        if (img.at<uint8_t>(p_car_y, p_car_x) == 255)
                        {
                            collision_free = false;
                            break;
                        }
                    }
                    Trajectory.push_back(cs);
                    t += dt_;
                }
                if (collision_free)
                {
                    TrajectoryList.push_back(Trajectory);
                    idx_vector.push_back(i);
                }
            }
            if (TrajectoryList.size() > 0)
            {
                break;
            }
        }
        auto end2 = std::chrono::high_resolution_clock::now();
        duration = end2 - end;
        // std::cout << "generate TrajectoryList cost time: " << duration.count() << " s" << std::endl;
        LOG_DEBUG("generate TrajectoryList cost time: {}", duration.count());
        // std::cout << "d_O====" << d_O << std::endl;
        std::vector<cv::Point> valley_bottom;
        std::vector<cv::Point> path;
        double r = expand_dis_;
        /*should take care about r > max(grid_res_) */
        // if(0.001 < d_O && (0.08 < expand_dis_ - d_O)){

        //     r = expand_dis_ * beta_ * expand_dis_ / d_O;
        // }
        // std::cout << "expand r = " << r << std::endl;
        LOG_DEBUG("expand r =  {}", r);
        if (TrajectoryList.size() > 0)
        {
            /*use findContours to decide normal walk or something else*/
            cv::Mat dm_img_EDT = img.clone();
            for (int x = 0; x < cols; x++)
            {
                for (int y = 0; y < rows; y++)
                {
                    dm_img_EDT.at<uint8_t>(y, x) = 255 - grid_res_[y][x] / max_val * 255;
                }
            }
            cv::Mat img_expand;
            int safe_dis = 255 - r / resolution_ / max_val * 255;
            cv::threshold(dm_img_EDT, img_expand, safe_dis, 255, cv::THRESH_BINARY);

            std::vector<std::vector<cv::Point>> origin_contours;
            std::vector<cv::Vec4i> hierarchy;
            std::vector<std::vector<cv::Point>> filter_contours;
            cv::findContours(img_expand, origin_contours, hierarchy, cv::RETR_CCOMP, cv::CHAIN_APPROX_SIMPLE);
            for (size_t i = 0; i < origin_contours.size(); i++)
            {
                std::vector<cv::Point> sub_contour;
                for (size_t j = 0; j < origin_contours[i].size(); j++)
                {
                    if (origin_contours[i][j].x > 15 &&
                        origin_contours[i][j].x < cols - 16 &&
                        origin_contours[i][j].y > look_ahead_dis_ &&
                        origin_contours[i][j].y < rows - 2)
                    {
                        cv::Point p(origin_contours[i][j].x, origin_contours[i][j].y);
                        sub_contour.push_back(p);
                    }
                }
                // std::cout << "origin_contours size :" << origin_contours[i].size() << std::endl;
                LOG_DEBUG("origin_contours size :  {}", origin_contours[i].size());
                // std::cout << "sub_contour point num:" << sub_contour.size() << std::endl;
                LOG_DEBUG("sub_contour point num:  {}", sub_contour.size());
                if ((int)sub_contour.size() >= min_counter_size_)
                {
                    filter_contours.push_back(sub_contour);
                }
            }

            auto end = std::chrono::high_resolution_clock::now();
            std::chrono::duration<double> duration = end - start;
            // std::cout << "dm cost " << duration.count() << "seconds" << std::endl;
            LOG_DEBUG("dm cost: {} seconds", duration.count());

            /*narrow passage*/
            if (filter_contours.size() == 2)
            {
                for (int i = 220; i < rows_; i++)
                {
                    double maxValue = -std::numeric_limits<double>::max();
                    cv::Point maxIndex;
                    for (int j = 0; j < cols; j++)
                    {
                        if (grid_res_[i][j] > maxValue)
                        {
                            maxIndex.x = j;
                            maxIndex.y = i;
                            maxValue = grid_res_[i][j];
                        }
                    }
                    path.emplace_back(maxIndex);
                }
                // std::cout << "narrow passage" << std::endl;
                LOG_DEBUG("narrow passage");
            }
            if (filter_contours.size() == 1) /*normal walk*/
            {
                path = filter_contours[0];
                // std::cout << "normal walk" << std::endl;
                LOG_DEBUG("normal walk");
            }
            // else if (filter_contours.size() == 1) /*normal walk*/
            // {
            //     path = filter_contours[0];
            //     std::cout << "normal walk" << std::endl;
            // }
            // else /*more complex case*/
            // {
            //     int lookahead = int(ctrl_v * predict_time_ / resolution_) + 1;
            //     for (int y = rows - 1; y > rows - lookahead; y--)
            //     {
            //         for (int x = cols - 1; x > -1; x--)
            //         {
            //             double err = std::pow(grid_res_[y][x] * resolution_ - r, 2);
            //             if (err < 1e-3)
            //             {
            //                 cv::Point p(x, y);
            //                 path.push_back(p);
            //                 break;
            //             }
            //         }
            //     }
            //     std::cout << "more complex case" << std::endl;
            // }
            ref_path_ = GetRefPath(path);
            // std::cout << "ref_path_ size: " << ref_path_.size() << std::endl;
            LOG_DEBUG("ref_path_ size: {}", ref_path_.size());
            if (!ref_path_.empty())
            {
                get_ref_path_ = true;
                // Pose target;
                switch (find_target_)
                {
                case 1:
                {
                    double max_dis = std::numeric_limits<double>::min();
                    int target_idx = std::numeric_limits<int>::max();
                    for (size_t i = 0; i < ref_path_.size(); i++)
                    {
                        double d = std::hypot(ref_path_.at(i).x, ref_path_.at(i).y);
                        if (d > max_dis)
                        {
                            max_dis = d;
                            target_idx = i;
                        }
                    }
                    target_.x = ref_path_.at(target_idx).x;
                    target_.y = ref_path_.at(target_idx).y;
                    // std::cout << "find_target:" << 1 << endl;
                    LOG_DEBUG("find_target: 1");
                    break;
                }

                case 2:
                {
                    std::priority_queue<Pose, std::vector<Pose>, Compare_y> que_y;
                    /*选取ref_path_位于x>100区域的y最大的点*/
                    for (size_t i = 0; i < ref_path_.size(); i++)
                    {
                        if (ref_path_.at(i).x > 100 * resolution_)
                        {
                            que_y.push(ref_path_.at(i));
                        }
                    }
                    if (!que_y.empty())
                    {
                        target_ = que_y.top();
                    }
                    else
                    {
                        for (size_t i = 0; i < ref_path_.size(); i++)
                        {
                            que_y.push(ref_path_.at(i));
                        }
                        target_ = que_y.top();
                    }
                    // std::cout << "find_target:" << 2 << endl;
                    LOG_DEBUG("find_target: 2");
                    break;
                }
                case 3: /*选取所有可行轨迹在ref_path_上的所有投影点中x最大的点*/
                {
                    std::priority_queue<Pose, std::vector<Pose>, Compare_x> que_x;
                    for (size_t i = 0; i < TrajectoryList.size(); i++)
                    {
                        std::vector<Pose> traj = TrajectoryList.at(i);
                        double min_distance = std::numeric_limits<double>::max();
                        size_t idx = 0;
                        for (size_t j = 0; j < ref_path_.size(); j++)
                        {
                            double distance = hypot(traj.back().x - ref_path_.at(j).x, traj.back().y - ref_path_.at(j).y);
                            if (distance < min_distance)
                            {
                                min_distance = distance;
                                idx = j;
                            }
                        }
                        que_x.push(ref_path_.at(idx));
                    }
                    if (!que_x.empty())
                    {
                        target_ = que_x.top();
                    }
                    else
                    {
                        for (size_t i = 0; i < ref_path_.size(); i++)
                        {
                            que_x.push(ref_path_.at(i));
                        }
                        target_ = que_x.top();
                    }
                    // std::cout << "find_target:" << 3 << endl;
                    LOG_DEBUG("find_target: 3");
                    break;
                }
                case 4: /*选取所有可行轨迹在ref_path_上的所有投影点中距离机器人位置最远的点*/
                {
                    std::priority_queue<Pose, std::vector<Pose>, Compare_d> que_d;
                    for (size_t i = 0; i < TrajectoryList.size(); i++)
                    {
                        std::vector<Pose> traj = TrajectoryList.at(i);
                        double min_distance = std::numeric_limits<double>::max();
                        size_t idx = 0;
                        for (long unsigned int j = 0; j < ref_path_.size(); j++)
                        {
                            double distance = hypot(traj.back().x - ref_path_.at(j).x, traj.back().y - ref_path_.at(j).y);
                            if (distance < min_distance)
                            {
                                min_distance = distance;
                                idx = j;
                            }
                        }
                        que_d.push(ref_path_.at(idx));
                    }
                    if (!que_d.empty())
                    {
                        target_ = que_d.top();
                    }
                    else
                    {
                        for (size_t i = 0; i < ref_path_.size(); i++)
                        {
                            que_d.push(ref_path_.at(i));
                        }
                        target_ = que_d.top();
                    }
                    // std::cout << "find_target:" << 4 << std::endl;
                    LOG_DEBUG("find_target: 4");
                    break;
                }
                default:
                    // std::cout << "please choose the right find target model" << std::endl;
                    LOG_DEBUG("please choose the right find target model");
                    break;
                }
                // std::cout << "target x:" << target_.x << "y: " << target_.y << std::endl;
                LOG_DEBUG("target x {} , target y {}", target_.x, target_.y);

                int best_idx = -1;
                double best_score = std::numeric_limits<double>::max();
                std::vector<double> score_twirling_vector;
                std::vector<double> score_path_follow_vector;
                std::vector<double> score_path_angle_vector;

                double sum_score_twirling = 0;
                double sum_score_path_follow = 0;
                double sum_score_path_angle = 0;

                for (size_t i = 0; i < TrajectoryList.size(); i++)
                {
                    int yaw_idx = idx_vector.at(i);
                    double score_twirling = Twirling(yaw_speed_vector.at(yaw_idx));
                    double score_path_follow = PathFollowCritic(TrajectoryList[i]);
                    double score_path_angle = PathAngleCritic(TrajectoryList[i]);

                    score_twirling_vector.push_back(score_twirling);
                    score_path_follow_vector.push_back(score_path_follow);
                    score_path_angle_vector.push_back(score_path_angle);

                    sum_score_twirling += score_twirling;
                    sum_score_path_follow += score_path_follow;
                    sum_score_path_angle += score_path_angle;
                }
                for (long unsigned int j = 0; j < score_twirling_vector.size(); j++)
                {
                    if (sum_score_twirling != 0)
                    {
                        score_twirling_vector.at(j) = score_twirling_vector.at(j) / sum_score_twirling;
                    }
                    else
                    {
                        // std::cout << "sum_score_twirling = 0!" << std::endl;
                        LOG_DEBUG("sum_score_twirling = 0");
                    }
                    if (sum_score_path_follow != 0)
                    {
                        score_path_follow_vector.at(j) = score_path_follow_vector.at(j) / sum_score_path_follow;
                    }
                    else
                    {
                        // std::cout << "sum_score_path_follow = 0!" << std::endl;
                        LOG_DEBUG("sum_score_path_follow = 0");
                    }

                    if (sum_score_path_angle != 0)
                    {
                        score_path_angle_vector.at(j) = score_path_angle_vector.at(j) / sum_score_path_angle;
                    }
                    else
                    {
                        // std::cout << "sum_score_path_angle = 0!" << std::endl;
                        LOG_DEBUG("sum_score_path_angle = 0");
                    }

                    double sum_score = score_twirling_vector.at(j) * twirling_scale_ +
                                       score_path_follow_vector.at(j) * path_follow_scale_ +
                                       score_path_angle_vector.at(j) * path_angle_scale_;
                    if (best_score > sum_score)
                    {
                        best_idx = j;
                        best_score = sum_score;
                    }
                }
                double ctrl_yaw_idx = idx_vector.at(best_idx);
                double ctrl_yaw = yaw_speed_vector.at(ctrl_yaw_idx);
                ctrl.first = ctrl_v;
                ctrl.second = ctrl_yaw;
                auto end3 = std::chrono::high_resolution_clock::now();
                duration = end3 - end2;
                // std::cout << "find path and score cost " << duration.count() << "s" << std::endl;
                LOG_DEBUG("find path and score cost {} s", duration.count());
            }
            else
            {
                get_ref_path_ = false;
                // std::cout << "can not get ref_path_!!" << std::endl;
                LOG_DEBUG("can not get ref_path_!!");
            }
            all_traj_crash_ = false;
        }
        else
        {
            all_traj_crash_ = true;
            // std::cout << "all trajectory crash!!" << std::endl;
            LOG_DEBUG("all trajectory crash!!");
        }
        auto end4 = std::chrono::high_resolution_clock::now();
        duration = end4 - start;
        // std::cout << "tolly cost" << duration.count() << "s" << std::endl;
        LOG_DEBUG("tolly cost {} s", duration.count());
        return ctrl;
    }

    // def L2I(robot_pose,pos_in):
    // x = pos_in[0] * math.cos(robot_pose[2]) - pos_in[1] * math.sin(robot_pose[2]) + robot_pose[0]
    // y = pos_in[0] * math.sin(robot_pose[2]) + pos_in[1] * math.cos(robot_pose[2]) + robot_pose[1]
    // y = -y
    // out = [x,y]
    // return out
    Pose L2I(Pose robot_pose, Pose pos_in)
    {
        Pose out;
        double x = pos_in.x * cos(robot_pose.theta) - pos_in.y * sin(robot_pose.theta) + robot_pose.x;
        double y = pos_in.x * sin(robot_pose.theta) + pos_in.y * cos(robot_pose.theta) + robot_pose.y;
        y = -y;
        out.x = x;
        out.y = y;
        out.theta = pos_in.theta;
        return out;
    }

    /*mark:if the img is all grass the grid_res may all fill with std::numeric_limits<double>::max()*/
    // int CheckSafeAndGenerateSDF(cv::Mat img)
    // {
    //     auto start = std::chrono::high_resolution_clock::now();
    //     int cols = img.cols;
    //     int rows = img.rows;
    //     cv::Mat invertedImage;
    //     cv::bitwise_not(img,invertedImage);
    //     std::cout<<"GenerateSDF GET a img :"<<cols<<","<<rows<<std::endl;

    //     for (int x = 0; x < cols; x++)
    //     {
    //         fillESDF([&](int y){return img.at<uint8_t>(y,x)> 128 ?
    //             0: std::numeric_limits<double>::max();} ,[&](int y,double val){grid_val_[y][x] = val;}  , 0, rows-1, rows);

    //         fillESDF([&](int y){return img.at<uint8_t>(y,x)> 128 ?
    //             0: std::numeric_limits<double>::max();} ,[&](int y,double val){grid_val_neg_[y][x] = val;}  , 0, rows-1, rows);
    //     }
    //     for (int  y = 0; y < rows; y++)
    //     {
    //         fillESDF([&](int x){return grid_val_[y][x];} ,[&](int x,double val){grid_res_[y][x] = std::sqrt(val);}  , 0, cols-1, cols);

    //         fillESDF([&](int x){return grid_val_[y][x];} ,[&](int x,double val){grid_res_neg_[y][x] = std::sqrt(val);}  , 0, cols-1, cols);
    //     }

    //     /*combine*/
    //     double k = positive_sdf_gain_;
    //     double r = expand_dis_;
    //     std::vector<std::pair<int,int>> valley_bottom;
    //     for (int x = 0; x < cols; x++)
    //     {
    //         for (int y = 0; y < rows; y++)
    //         {
    //             grid_res_all_[y][x] = grid_res_[y][x];
    //             if (grid_res_neg_[y][x] > 0)
    //             {
    //                 grid_res_all_[y][x] += -grid_res_neg_[y][x];
    //             }
    //             grid_res_all_[y][x] *= resolution_;
    //             field_obs_[y][x] = k * std::pow(grid_res_all_[y][x] - r ,2);
    //             if (field_obs_[y][x] == 0)
    //             {
    //                 valley_bottom.push_back(make_pair(y,x));
    //             }
    //         }
    //     }

    //     /*sample*/
    //     // while t < config.predict_time:
    //     //         nstate = np.zeros(3)
    //     //         nyaw = cs[2] + i * config.dt
    //     //         nx = cs[0] + v * math.cos(nyaw) * config.dt
    //     //         ny = cs[1] + v * math.sin(nyaw) * config.dt
    //     //         nstate[0] = nx
    //     //         nstate[1] = ny
    //     //         nstate[2] = nyaw
    //     //         cs = nstate
    //     //         trajectory = np.vstack((trajectory,nstate))
    //     //         t += config.dt
    //     // for (double i = ; i < count; i++)
    //     // {
    //     //     /* code */
    //     // }
    //     read_table();
    //     vector<double>score_vector;
    //     double minScore = std::numeric_limits<double>::max();  // 最低分
    //     std::vector<int> minIndices;  // 存储最低分轨迹的索引
    //     if (lookup_table_.size() == yaw_speed_.size() && lookup_table_[0].size()!=0)
    //     {
    //         for (int i = 0; i < lookup_table_.size(); i++)
    //         {
    //             double score=0;
    //             for (int j = 0; j < lookup_table_.at(i).size(); j++)
    //             {
    //                 double x = lookup_table_[i][j].first;
    //                 double y = lookup_table_[i][j].second;
    //                 score += field_obs_[y][x];
    //             }
    //             score_vector.push_back(score);
    //             if (score < minScore) {
    //                 minScore = score;
    //                 minIndices.clear();
    //                 minIndices.push_back(i);  // 更新最低分索引
    //             } else if (score == minScore) {
    //                 minIndices.push_back(i);  // 添加相同最低分的索引
    //             }
    //         }
    //         if(minIndices.size() == 1){
    //             return minIndices.at(0);
    //         }
    //         else if(minIndices.size() > 1){
    //             int min_yaw_idx;
    //             double min_yaw = std::numeric_limits<double>::max();
    //             for (auto k:minIndices)
    //             {
    //                 if (min_yaw > yaw_speed_.at(k))
    //                 {
    //                     min_yaw = yaw_speed_.at(k);
    //                     min_yaw_idx = k;
    //                 }
    //             }
    //             auto end = std::chrono::high_resolution_clock::now();
    //             std::chrono::duration<double> duration = end - start;
    //             std::cout<<"sdf cost"<<duration.count()<<"seconds"<<std::endl;
    //             return min_yaw_idx;

    //         }
    //         else{
    //             std::cout<<"minIndices size < 1"<<std::endl;
    //             auto end = std::chrono::high_resolution_clock::now();
    //             std::chrono::duration<double> duration = end - start;
    //             std::cout<<"sdf cost"<<duration.count()<<"seconds"<<std::endl;
    //             return -1;
    //         }
    //     }

    //     std::cout<<"lookuptable size wrong!"<<std::endl;
    //     auto end = std::chrono::high_resolution_clock::now();
    //     std::chrono::duration<double> duration = end - start;
    //     std::cout<<"sdf cost"<<duration.count()<<"seconds"<<std::endl;
    //     return -1;
    // }

    std::vector<Twist2D> SampleVelocity(Twist2D current_velocity)
    {
        std::vector<Twist2D> sample_velocitys;
        Twist2D sample_velocity;
        double v_x = current_velocity.x;
        double v_theta = current_velocity.theta;

        if (v_x < min_v_x_)
        {
            v_x = min_v_x_;
        }
        if (v_x > max_v_x_)
        {
            v_x = max_v_x_;
        }

        if (v_theta < min_v_theta_)
        {
            v_theta = min_v_theta_;
        }
        if (v_theta > max_v_theta_)
        {
            v_theta = max_v_theta_;
        }

        /*calculate v_x sample*/
        double max_v_x = v_x + accel_x_ * sim_time_;
        max_v_x = std::min(max_v_x_, max_v_x);

        double min_v_x = v_x + decel_x_ * sim_time_;
        min_v_x = std::max(min_v_x_, min_v_x);

        double increment_x = (max_v_x - min_v_x) / std::max(1, sample_x_ - 1);

        /*calculate theta sample*/
        double max_v_theta = v_theta + accel_theta_ * sim_time_;
        max_v_theta = std::min(max_v_theta, max_v_theta_);

        double min_v_theta = v_theta + decel_theta_ * sim_time_;
        min_v_theta = std::max(min_v_theta_, min_v_theta);

        double increment_theta = (max_v_theta - min_v_theta) / std::max(1, sample_theta_ - 1);

        for (int i = 0; i < sample_x_; i++)
        {
            for (int k = 0; k < sample_theta_; k++)
            {
                sample_velocity.x = min_v_x + increment_x * i;
                sample_velocity.theta = min_v_theta + increment_theta * k;
                sample_velocitys.push_back(sample_velocity);
            }
            if (max_v_theta >= 0 && min_v_theta <= 0)
            {
                sample_velocity.x = min_v_x + increment_x * i;
                sample_velocity.theta = 0;
                sample_velocitys.push_back(sample_velocity);
            }
        }
        return sample_velocitys;
    }

    std::vector<std::vector<Pose>> Generator(Twist2D current_velocity, Pose start_pose, std::vector<Twist2D> sample_res)
    {
        std::vector<std::vector<Pose>> trajectorys;
        std::vector<Pose> trajectory;
        trajectorys.clear();
        for (long unsigned int i = 0; i < sample_res.size(); i++)
        {
            trajectory = GeneratorTrajectory(current_velocity, start_pose, sample_res.at(i), sim_time_, dt_);
            trajectorys.push_back(trajectory);
        }
        return trajectorys;
    }

    std::vector<Pose> GeneratorTrajectory(Twist2D start_velocity, Pose start_pose, Twist2D target, double sim_time, double dt)
    {
        std::vector<Pose> trajectory;
        Twist2D cur_vel = start_velocity;
        Pose cur_pose = start_pose;
        trajectory.push_back(cur_pose);
        for (double time = 0; time <= sim_time; time += dt)
        {
            cur_vel = ComputeNewVelocity(target, cur_vel, dt);
            cur_pose = ComputeNewPosition(cur_pose, cur_vel, dt);
            trajectory.push_back(cur_pose);
        }
        return trajectory;
    }

    Twist2D ComputeNewVelocity(Twist2D cmd_vel, Twist2D cur_vel, double dt)
    {
        Twist2D new_vel;

        if (cur_vel.x < cmd_vel.x)
        {
            new_vel.x = cur_vel.x + accel_x_ * dt;
            new_vel.x = std::min(cmd_vel.x, new_vel.x);
        }
        else
        {
            new_vel.x = cur_vel.x + decel_x_ * dt;
            new_vel.x = std::max(cmd_vel.x, new_vel.x);
        }

        if (cur_vel.theta < cmd_vel.theta)
        {
            new_vel.theta = cur_vel.theta + accel_theta_ * dt;
            new_vel.theta = std::min(cmd_vel.theta, new_vel.theta);
        }
        else
        {
            new_vel.theta = cur_vel.theta + decel_theta_ * dt;
            new_vel.theta = std::max(cmd_vel.theta, new_vel.theta);
        }
        return new_vel;
    }

    Pose ComputeNewPosition(Pose cur_pose, Twist2D cur_vel, double dt)
    {
        Pose new_pose;
        new_pose.x = cur_pose.x + cur_vel.x * cos(cur_pose.theta) * dt;
        new_pose.y = cur_pose.y + cur_vel.x * sin(cur_pose.theta) * dt;
        new_pose.theta = cur_pose.theta + cur_vel.theta * dt;
        return new_pose;
    }

    int ChooseBestTraj(const std::vector<Twist2D> &sample_Twists, const std::vector<std::vector<Pose>> &TrajectoryList, const std::vector<int> &idx)
    {
        // std::ofstream outfile_score;
        // outfile_score.open("/root/score_data.txt",std::ios::out|std::ios::app);
        // bool open_flash;
        // if(!outfile_score.is_open()){
        //     open_flash = false;
        //     std::cout<<"can not open score_data.txt"<<std::endl;
        // }
        // else{
        //     open_flash = true;
        // }
        int best_idx = -1;
        double best_score = std::numeric_limits<double>::max();
        std::vector<double> score_twirling_vector;
        std::vector<double> score_perfer_forward_vector;
        std::vector<double> score_path_follow_vector;
        std::vector<double> score_path_angle_vector;

        double sum_score_twirling = 0;
        double sum_score_perfer_forward = 0;
        double sum_score_path_follow = 0;
        double sum_score_path_angle = 0;

        FindTarget(TrajectoryList, idx);
        for (int i : idx)
        {
            double score_twirling = Twirling(sample_Twists[i]);
            double score_perfer_forward = PerferForward(sample_Twists[i]);
            double score_path_follow = PathFollowCritic(TrajectoryList[i]);
            double score_path_angle = PathAngleCritic(TrajectoryList[i]);

            score_twirling_vector.push_back(score_twirling);
            score_perfer_forward_vector.push_back(score_perfer_forward);
            score_path_follow_vector.push_back(score_path_follow);
            score_path_angle_vector.push_back(score_path_angle);

            sum_score_twirling += score_twirling;
            sum_score_perfer_forward += score_perfer_forward;
            sum_score_path_follow += score_path_follow;
            sum_score_path_angle += score_path_angle;
        }
        for (long unsigned int j = 0; j < score_twirling_vector.size(); j++)
        {
            if (sum_score_twirling != 0)
            {
                score_twirling_vector.at(j) = score_twirling_vector.at(j) / sum_score_twirling;
            }
            else
            {
                std::cout << "sum_score_twirling = 0!" << std::endl;
            }
            if (sum_score_perfer_forward != 0)
            {
                score_perfer_forward_vector.at(j) = score_perfer_forward_vector.at(j) / sum_score_perfer_forward;
            }
            else
            {
                std::cout << "sum_score_perfer_forward = 0!" << std::endl;
            }
            if (sum_score_path_follow != 0)
            {
                score_path_follow_vector.at(j) = score_path_follow_vector.at(j) / sum_score_path_follow;
            }
            else
            {
                std::cout << "sum_score_path_follow = 0!" << std::endl;
            }

            if (sum_score_path_angle != 0)
            {
                score_path_angle_vector.at(j) = score_path_angle_vector.at(j) / sum_score_path_angle;
            }
            else
            {
                std::cout << "sum_score_path_angle = 0!" << std::endl;
            }

            double sum_score = score_twirling_vector.at(j) * twirling_scale_ +
                               score_perfer_forward_vector.at(j) * perfer_forward_scale_ +
                               score_path_follow_vector.at(j) * path_follow_scale_ +
                               score_path_angle_vector.at(j) * path_angle_scale_;

            // if(open_flash){
            //     outfile_score << score_twirling_vector.at(j) << " " << score_perfer_forward_vector.at(j)<<" "<<score_path_follow_vector.at(j)<<" "<<score_path_angle_vector.at(j)<<std::endl;
            // }

            if (best_score > sum_score)
            {
                best_idx = idx.at(j);
                best_score = sum_score;
            }
        }
        last_vel_linear_ = sample_Twists.at(best_idx).x;
        last_vel_angular_ = sample_Twists.at(best_idx).theta;
        // if(open_flash){
        //     outfile_score << "-------------------------best idx : " << best_idx <<std::endl;
        // }
        // outfile_score.close();
        return best_idx;
    }

    double Twirling(const Twist2D &vel)
    {
        return pow(fabs(vel.theta), 2);
    }

    double Twirling(const double &vel)
    {
        return pow(fabs(vel), 2);
    }

    double PerferForward(const Twist2D &vel)
    {
        if (vel.x < 0.0 || vel.x > 0.2)
        {
            return perfer_forward_penalty_; // penalty
        }
        /*防止陷入局部最小点，车辆原地不动*/
        if (vel.x < perfer_forward_strafe_x_ && vel.theta < perfer_forward_strafe_theta_)
        {
            return perfer_forward_penalty_;
        }
        if (fabs(vel.x - last_vel_linear_) > 0.1 || fabs(vel.theta - last_vel_angular_) > 0.25)
        {
            return perfer_forward_penalty_;
        }
        return fabs(vel.theta) * theta_scale_;
    }
    /*参考线ref_path_,预留安全距离0.15m*/
    double FitGuideLine(const std::vector<Pose> &traj, const double &safe_distance)
    {
        double distance = -1;
        if (ref_path_.empty())
        {
            std::cout << "have no re_path_ !" << std::endl;
        }
        else
        {
            double x = traj.back().x;
            double y = traj.back().y;
            // double theta = traj.back().theta;
            double a = (ref_path_.at(0).y - ref_path_.at(1).y) / (ref_path_.at(0).x - ref_path_.at(1).x);
            double b = ref_path_.at(0).y - a * ref_path_.at(0).x;
            distance = std::abs(y - a * x - b) / (std::sqrt(a * a + 1));
            distance = (distance - safe_distance) * (distance - safe_distance);
        }
        return distance;
    }

    double PathFollowCritic(const std::vector<Pose> &traj)
    {
        double sum_dis = 0;
        if (ref_path_.empty())
        {
            std::cout << "PathFollowCritic have no re_path_ !" << std::endl;
            return 0;
        }

        // int proj_idx = -1;
        for (long unsigned int j = 0; j < traj.size(); j++)
        {
            double min_distance = std::numeric_limits<double>::max();
            for (long unsigned int i = 0; i < ref_path_.size(); i++)
            {
                double distance = hypot(traj.at(j).x - ref_path_.at(i).x, traj.at(j).y - ref_path_.at(i).y);
                if (distance < min_distance)
                {
                    min_distance = distance;
                }
            }
            sum_dis += min_distance;
        }

        double average_distance = sum_dis / traj.size();
        return std::pow(average_distance, 2);
    }

    void FindTarget(const std::vector<std::vector<Pose>> &TrajectoryList, const std::vector<int> idx)
    {
        (void)idx;
        (void)TrajectoryList;
        // TODO:如果ref_path_为空那么目标点应该定为什么呢？是否可以是车辆位置？
        if (ref_path_.empty())
        {
            target_.x = 0.0;
            target_.y = 0.0;
        }
        else
        {
            std::priority_queue<Pose, std::vector<Pose>, Compare> que;
            /*选取所有可行轨迹在ref_path_上的所有投影点中x最大的点*/
            /*int target_idx = std::numeric_limits<int>::max();
            for (auto i:idx)
            {
                std::vector<Pose> traj = TrajectoryList.at(i);
                double min_distance = std::numeric_limits<double>::max();
                double idx;
                for (long unsigned int  j = 0; j < ref_path_.size(); j++)
                {
                    double distance = hypot(traj.back().x - ref_path_.at(j).x,traj.back().y - ref_path_.at(j).y);
                    if (distance<min_distance)
                    {
                        min_distance = distance;
                        idx = j;
                    }
                }
                if ( idx < target_idx )
                {
                    target_idx = idx ;
                }
                que.push(ref_path_.at(idx));
            }*/
            /*选取ref_path_位于x>100区域的y最大的点*/
            for (size_t i = 0; i < ref_path_.size(); i++)
            {
                if (ref_path_.at(i).x > 100 * resolution_)
                {
                    que.push(ref_path_.at(i));
                }
            }
            if (!que.empty())
            {
                target_ = que.top();
            }
            else
            {
                for (size_t i = 0; i < ref_path_.size(); i++)
                {
                    que.push(ref_path_.at(i));
                }
                target_ = que.top();
            }
            // target_ = ref_path_.at(target_idx);
            std::cout << "target pose x=" << target_.x << " y= " << target_.y << std::endl;
            // auto msg = geometry_msgs::msg::PointStamped();
            // msg.header.stamp = this->now();
            // msg.header.frame_id = "map";
            // msg.point.x = target_.x;
            // msg.point.y = target_.y;
            // msg.point.z = 0;
            // target_pub_ ->publish(msg);
        }
    }

    double ShortestAngularDistance(const double &angle1, const double &angle2)
    {
        return NormalizeAngleRadians(angle1 - angle2);
    }

    double NormalizeAngleRadians(double angle)
    {
        angle = std::fmod(angle, 2.0 * M_PI);
        if (angle < -M_PI)
        {
            angle += 2.0 * M_PI;
        }
        else if (angle >= M_PI)
        {
            angle -= 2.0 * M_PI;
        }
        return angle;
    }

    double PathAngleCritic(const std::vector<Pose> &traj)
    {
        if (ref_path_.empty())
        {
            std::cout << "PathAngleCritic have no re_path_ !" << std::endl;
            return 0;
        }
        double sum_angle = 0;
        for (long unsigned int i = 0; i < traj.size(); i++)
        {
            double yaw_between_points = std::atan2(target_.y - traj.at(i).y,
                                                   target_.x - traj.at(i).x);
            double yaw = fabs(ShortestAngularDistance(traj.at(i).theta, yaw_between_points));
            sum_angle += yaw;
        }
        return std::pow(sum_angle / traj.size(), 2);
    }

    int W2P(double x, double resolution)
    {
        return std::floor(x / resolution);
    }

    /*
        需要注意效果是否需要将地图数据进行转换
        由于bev上的点是相机位置，可以等效为前轴中心，因此只对该点以及前轴左右轮做碰撞检测
    */

    std::vector<int> FindFreeTraj(const std::vector<std::vector<Pose>> &TrajectoryList, cv::Mat in_img)
    {
        (void)in_img;
        std::vector<int> free_idx;
        cv::Mat img;
        if (Bin_map_.empty())
        {
            std::cout << "Bin_map_ is empty!" << std::endl;
            return free_idx;
        }
        else
        {
            img = Bin_map_.clone();
            // img = in_img;
        }
        // img = in_img;
        int cols = img.cols;
        int rows = img.rows;
        for (long unsigned int i = 0; i < TrajectoryList.size(); i++)
        {
            auto traj = TrajectoryList[i];
            bool collision_free = true;
            int duration = (traj.size() - 1) / 10;
            for (int j = TrajectoryList[i].size() - 1; -1 < j; j = j - duration)
            {
                double x = traj.at(j).x;
                double y = traj.at(j).y;
                double theta = traj.at(j).theta;

                // double f_r_x = sin(theta) * front_axle_ / 2 + x;
                // double f_r_y = -cos(theta) * front_axle_ / 2 + y;

                // double f_l_x = -sin(theta) * front_axle_ / 2 + x;
                // double f_l_y = cos(theta) * front_axle_ / 2 + y;

                // double b_r_x = -cos(theta) * vehicle_length_ + sin(theta) * back_axle_ / 2 + x;
                // double b_r_y = -sin(theta) * vehicle_length_ - cos(theta) * back_axle_ / 2 + y;

                // double b_l_x = -cos(theta) * vehicle_length_ - sin(theta) * back_axle_ / 2 + x;
                // double b_l_y = -sin(theta) * vehicle_length_ + cos(theta) * back_axle_ / 2 + y;

                /*判断车身是否会超过bev*/
                // if (b_l_x > 0.9 || b_l_x < 0 || b_l_y > 0.5 || b_l_y < -0.5 ||
                //     b_r_x > 0.9 || b_r_x < 0 || b_r_y > 0.5 || b_r_y < -0.5 ||
                //     f_l_x > 0.9 || f_l_x < 0 || f_l_y > 0.5 || f_l_y < -0.5 ||
                //     f_r_x > 0.9 || f_r_x < 0 || f_r_y > 0.5 || f_r_y < -0.5)
                // {
                //     collision_free = false;
                //     break;
                // }

                /*将车身坐标系的点首先转化为map坐标系,然后再反转y转换到图像坐标系,最后通过像素化进行*/
                VecXYW car(x, y, theta);

                // VecXYW b_l(b_l_x,b_l_y,0.0);
                // VecXYW b_r(b_r_x,b_r_y,0.0);
                // VecXYW f_l(f_l_x,f_l_y,0.0);
                // VecXYW f_r(f_r_x,f_r_y,0.0);

                VecXYW robot_pose(robot_in_map_x_ * resolution_, robot_in_map_y_ * resolution_, robot_in_map_yaw_);
                VecXYW car_map = L2G(robot_pose, car);

                // VecXYW b_l_map = L2G(robot_pose,b_l);
                // VecXYW b_r_map = L2G(robot_pose,b_r);
                // VecXYW f_l_map = L2G(robot_pose,f_l);
                // VecXYW f_r_map = L2G(robot_pose,f_r);

                int p_car_x = W2P(car_map.x, resolution_);
                int p_car_y = W2P(-car_map.y, resolution_);

                // int p_b_l_x = W2P(b_l_map.x,resolution_);
                // int p_b_l_y = W2P(-b_l_map.y,resolution_);
                // int p_b_r_x = W2P(b_r_map.x,resolution_);
                // int p_b_r_y = W2P(-b_r_map.y,resolution_);
                // int p_f_l_x = W2P(f_l_map.x,resolution_);
                // int p_f_l_y = W2P(-f_l_map.y,resolution_);
                // int p_f_r_x = W2P(f_r_map.x,resolution_);
                // int p_f_r_y = W2P(-f_r_map.y,resolution_);

                /*判断轨迹点是否处于不可行区域*/
                // std::cout<<"----------------------p_car_x:"<<p_car_x<<"----------p_car_y:"<<p_car_y<<std::endl;
                if (0 < p_car_x && p_car_x < cols && 0 < p_car_y && p_car_y < rows)
                {
                    // std::cout<<"----------img.at<uint8_t>(p_car_y,p_car_x) == 255---------"<<img.at<uint8_t>(p_car_y,p_car_x)<<std::endl;
                    if (img.at<uint8_t>(p_car_y, p_car_x) == 255)
                    {
                        collision_free = false;
                        break;
                    }
                }

                // if(0 < p_f_l_x && p_f_l_x < cols && 0 < p_f_l_y && p_f_l_y < rows)
                // {
                //     if (img.at<uint8_t>(p_f_l_y,p_f_l_x) == 255)
                //     {
                //         collision_free = false;
                //         break;
                //     }

                // }

                // if(0 < p_f_r_x && p_f_r_x < cols && 0 < p_f_r_y && p_f_r_y < rows)
                // {
                //     if (img.at<uint8_t>(p_f_r_y,p_f_r_x) == 255)
                //     {
                //         collision_free = false;
                //         break;
                //     }

                // }
            }
            if (collision_free)
            {
                free_idx.push_back(i);
            }
        }
        return free_idx;
    }
    /**
     * @brief 将图像坐标系下的参考路径转化为车身坐标系，当Path_edt_为空时候，返回的track_path_local也是空
     */
    std::vector<Pose> GetRefPath(const std::vector<cv::Point> &Path_edt_)
    {
        VecXYW odom_pose;
        // std::vector<cv::Point> temp_path;
        std::vector<VecXYW> temp_path;
        std::vector<Pose> track_path_local;
        /*将左手系转化为右手系*/
        for (long unsigned int i = 0; i < Path_edt_.size(); i++)
        {
            temp_path.push_back(VecXYW(Path_edt_.at(i).x * resolution_, -Path_edt_.at(i).y * resolution_, robot_in_map_yaw_));
        }
        /*将map坐标系转化为车身坐标系*/
        odom_pose.x = robot_in_map_x_ * resolution_;
        odom_pose.y = robot_in_map_y_ * resolution_;
        odom_pose.w = robot_in_map_yaw_;
        VecXYW goal;
        for (const auto &pose : temp_path)
        {
            goal.x = pose.x;
            goal.y = pose.y;

            // RCLCPP_DEBUG(this->get_logger(), "odom_pose在map坐标系下 (%f, %f)", odom_pose.x, odom_pose.x);
            // RCLCPP_DEBUG(this->get_logger(), "goal在map坐标系下 (%f, %f)", goal.x, goal.x);

            VecXYW local_point = G2L(odom_pose, goal);

            // RCLCPP_DEBUG(this->get_logger(), "local_point在小车坐标系下 (%f, %f)", local_point.x, local_point.x);

            Pose pose_local;
            pose_local.x = local_point.x;
            pose_local.y = local_point.y;
            // 增加沿边安全距离
            // pose_local.pose.position.y += safety_distance_along_edge_;

            track_path_local.push_back(pose_local);
            // RCLCPP_DEBUG(this->get_logger(), "pose_local在小车坐标系下 (%f, %f)", pose_local.pose.position.x, pose_local.pose.position.y);
        }
        return track_path_local;
    }

    bool get_ref_path_ = false;
    bool travelable_{false};
    bool all_traj_crash_{false};

private:
    bool initialized_;
    int cols_{400};
    int rows_{360};
    /*像素点与真实长度的分辨率*/
    double resolution_{0.0025};

    /*sample parameter*/
    double sim_time_{3.0};
    double dt_{0.1};
    double max_v_x_{0.2}; // 0.3
    double min_v_x_{0.1};
    double accel_x_{0.2};
    double decel_x_{0};
    double max_v_theta_{3.0 / 2};  // 3.0/2
    double min_v_theta_{-3.0 / 2}; // 1.57
    double accel_theta_{0.5};      // 1.0
    double decel_theta_{-0.5};
    int sample_x_{5};
    int sample_theta_{20};

    /*critic parameter*/
    double twirling_scale_{10.0}; // 10
    double perfer_forward_scale_{1.0};
    double path_follow_scale_{1.0};
    double path_angle_scale_{10.0};
    double perfer_forward_penalty_{1.0};
    double perfer_forward_strafe_x_{0.1};
    double perfer_forward_strafe_theta_{0.2};
    /*过大则无法对速度进行有效的约束*/
    double theta_scale_{0};

    /*车辆尺寸*/
    double front_axle_{0.46};
    double back_axle_{0.46};
    double vehicle_length_{0.53};

    Pose target_;

    std::vector<std::vector<double>> grid_val_;
    std::vector<std::vector<double>> grid_res_;
    std::vector<std::vector<double>> grid_val_neg_;
    /*保存图像的像素距离信息，其值会超过255*/
    std::vector<std::vector<double>> grid_res_neg_;
    std::vector<std::vector<double>> grid_res_all_;
    std::vector<std::vector<double>> field_obs_;
    /*保存经过edt处理后的距离地图,小于安全距离的区域为255(白色),用于collision check*/
    cv::Mat Bin_map_;

    /*沿边距离参数:expand_dis_ 用于沿边，expand_dis_check_ 用于 collision check*/
    double expand_dis_{0.25};
    double expand_dis_check_{0.001};

    /*Path_edt_ 坐标系为图像坐标系 ref_path 坐标系为车身坐标系*/
    std::vector<cv::Point> Path_edt_;
    std::vector<Pose> ref_path_;

    float robot_in_map_x_{200.0};  // 198.0
    float robot_in_map_y_{-420.0}; //-356.0
    float robot_in_map_yaw_{1.57};

    int robot_in_img_x_{198};
    int robot_in_img_y_{356};

    /*记录上个控制周期下发的线速度和角速度*/
    double last_vel_linear_{0};
    double last_vel_angular_{0};

    /*过滤数量小于该下限值的counter*/
    int min_counter_size_{2}; // 10
    /*用于过滤contours1的长度*/
    int look_ahead_dis_{220};

    /*sdf*/
    double positive_sdf_gain_{10.0};
    double negative_sdf_gain_{1.0};

    /*lookup table*/
    // std::vector<std::vector<std::pair<double, double>>> lookup_table_;
    // std::vector<double> yaw_speed_{-0.61086524,-0.43633231,-0.26179939,-0.08726646 ,0.08726646 ,0.26179939 ,0.43633231 ,0.0};
    double const_v{0.1};

    double max_yaw_speed_{0.6};
    double predict_time_{3.0};
    double travelable_num_{10};
    double d_prox_{0.25};
    double v_max_{0.2};
    double alpha_{1.0};
    double beta_{1.0};
    int find_target_{1};
    // Pose robot_pose_map_(198.0*0.0025,-356.0,M_PI/2);
};

} // namespace fescue_iox

#endif
