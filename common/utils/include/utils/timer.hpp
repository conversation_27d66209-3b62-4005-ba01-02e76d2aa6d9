#pragma once

#include <atomic>
#include <chrono>
#include <condition_variable>
#include <functional>
#include <iostream>
#include <thread>

namespace fescue_iox
{

class Timer
{
public:
    using Callback = std::function<void()>;

    Timer()
        : running_(false)
        , periodic_(false)
        , interval_ms_(0)
    {
    }

    ~Timer() { stop(); }

    void start(uint64_t interval_ms, bool periodic, Callback callback)
    {
        stop(); // 确保上一个定时器已关闭
        interval_ms_ = interval_ms;
        if (interval_ms_ == 0)
        {
            interval_ms_ = 1;
        }
        periodic_ = periodic;
        callback_ = std::move(callback);

        running_ = true;
        worker_thread_ = std::thread(&Timer::run, this);
    }

    void stop()
    {
        running_ = false;
        cv_.notify_one(); // 唤醒线程
        if (worker_thread_.joinable())
        {
            worker_thread_.join();
        }
    }

private:
    void run()
    {
        std::unique_lock<std::mutex> lock(mutex_);
        while (running_)
        {
            if (callback_ && running_)
            {
                callback_();
            }
            if (!periodic_)
            {
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(interval_ms_));
        }
    }

    std::atomic<bool> running_;
    bool periodic_;        // 是否周期执行
    uint64_t interval_ms_; // 执行间隔，单位毫秒
    Callback callback_;    // 执行回调

    std::thread worker_thread_; // 线程
    std::mutex mutex_;
    std::condition_variable cv_;
};

} // namespace fescue_iox

/*
int main()
{
    Timer timer;
    timer.start(1000, true, []() { std::cout << "Timer triggered!" << std::endl; });

    std::this_thread::sleep_for(std::chrono::seconds(5));
    timer.stop();
    return 0;
}
*/