#include "utils/slip_detector.hpp"

#include "utils/logger.hpp"
#include "utils/math_type.hpp"

#include <Eigen/Core>
#include <iostream>
#include <unsupported/Eigen/FFT>

namespace fescue_iox
{

std::pair<double, double> GetRobotSpeed(double motor_speed_left, double motor_speed_right)
{
    double wheel_radius = 0.1f;
    double wheel_base = 0.335f;
    float w_left = motor_speed_left * 2 * M_PI / 60.0f;
    float w_right = motor_speed_right * 2 * M_PI / 60.0f;
    float v_left = w_left * wheel_radius;
    float v_right = w_right * wheel_radius;
    float theoretical_linear = (v_right + v_left) / 2.0f;
    float theoretical_angular = (v_right - v_left) / wheel_base;
    return std::make_pair(theoretical_linear, theoretical_angular);
}

SlipDetector::SlipDetector()
    : turning_slip_ratio_(0.0)
    , moving_slip_ratio_(0.0)
    , odom_imu_angle_diff_(0.0)
    , turning_slip_start_time_(-1.0)
    , moving_slip_start_time_(-1.0)
    , last_check_moving_slip_time_(-1.0)
{
    Reset();
}

void SlipDetector::Update(double current_time, double ax, double ay, double az,
                          double gx, double gy, double gz, double left_motor_speed,
                          double right_motor_speed, bool is_motion)
{
    turning_slip_ratio_ = 0.0;
    moving_slip_ratio_ = 0.0;
    odom_imu_angle_diff_ = 0.0;
    is_freq_wheel_slip_ = false;
    if (imu_filter_ == nullptr)
    {
        imu_filter_ = std::make_shared<IMUFilter>(current_time);
        return;
    }
    // prepare data
    imu_filter_->Update(current_time, ax, ay, az, gx, gy, gz);
    auto angles = imu_filter_->GetAngles();
    double imu_yaw = angles(2);
    auto [odom_linear_velocity, odom_angular_velocity] = GetRobotSpeed(left_motor_speed, right_motor_speed);
    SlipDataUnit slip_data_unit(current_time, imu_yaw, odom_linear_velocity, odom_angular_velocity, ax, ay, az, gx, gy, gz);
    // store data
    slip_data_buffer_.push_back(slip_data_unit);
    size_t max_buffer_size = 200;
    if (slip_data_buffer_.size() > max_buffer_size)
    {
        slip_data_buffer_.pop_front();
    }
    double front_time = slip_data_buffer_.front().time;
    double back_time = slip_data_buffer_.back().time;
    double max_buffer_time = 1.0;
    double min_buffer_time = 0.7;
    while (fabs(back_time - front_time) > max_buffer_time)
    {
        slip_data_buffer_.pop_front();
        if (slip_data_buffer_.empty())
        {
            break;
        }
        front_time = slip_data_buffer_.front().time;
        back_time = slip_data_buffer_.back().time;
    }
    double min_motion_slip_linear = 0.07;
    double min_motion_slip_angular = 0.1;
    if (!is_motion && (fabs(odom_linear_velocity) > min_motion_slip_linear || fabs(odom_angular_velocity) > min_motion_slip_angular))
    {
        if (no_motion_start_time_ < 0)
        {
            no_motion_start_time_ = current_time;
        }
        else
        {
            double diff_time = current_time - no_motion_start_time_;
            double no_motion_slip_time = 0.5;
            if (diff_time > no_motion_slip_time)
            {
                if (fabs(odom_linear_velocity) < 0.05)
                {
                    turning_slip_ratio_ = 1.0;
                }
                else
                {
                    moving_slip_ratio_ = 1.0;
                }
                return;
            }
        }
    }
    else
    {
        no_motion_start_time_ = current_time;
    }
    if (slip_data_buffer_.size() < 2)
    {
        return;
    }
    front_time = slip_data_buffer_.front().time;
    back_time = slip_data_buffer_.back().time;
    if (back_time - front_time < min_buffer_time)
    {
        return;
    }
    CalculateTurningSlipRatio(current_time);
    CalculateMovingSlipRatio(current_time);
    CalculateFFTData();
}

std::vector<std::pair<double, double>> SlipDetector::FFTIMUAngularVelocity(const Eigen::VectorXd &signal) const
{
    double fs = 200.0;
    Eigen::FFT<double> fft;
    Eigen::VectorXcd spectrum;
    size_t data_size = signal.size();
    fft.fwd(spectrum, signal);
    Eigen::VectorXd magnitude = spectrum.cwiseAbs();
    std::vector<std::pair<double, double>> fft_data;
    for (int k = 0; k < spectrum.size(); ++k)
    {
        double freq = k * (fs / data_size);
        double magnitude_value = magnitude(k);
        fft_data.push_back(std::make_pair(freq, magnitude_value));
    }
    return fft_data;
}

void SlipDetector::CalculateFFTData()
{
    fft_gx_data_.clear();
    fft_gy_data_.clear();
    fft_gz_data_.clear();
    is_freq_wheel_slip_ = false;

    if (slip_data_buffer_.size() < 20)
    {
        return;
    }
    size_t data_size = slip_data_buffer_.size();
    Eigen::VectorXd gx_signal(data_size);
    // Eigen::VectorXd gy_signal(data_size);
    // Eigen::VectorXd gz_signal(data_size);
    for (size_t i = 0; i < data_size; ++i)
    {
        gx_signal[i] = slip_data_buffer_[i].gx;
        // gy_signal[i] = slip_data_buffer_[i].gy;
        // gz_signal[i] = slip_data_buffer_[i].gz;
    }
    fft_gx_data_ = FFTIMUAngularVelocity(gx_signal);
    // fft_gy_data_ = FFTIMUAngularVelocity(gy_signal);
    // fft_gz_data_ = FFTIMUAngularVelocity(gz_signal);

    // double max_main_freq = 110;
    // double min_main_freq = 90;
    // double main_amp = 0.8;
    // double max_minor_freq = 60;
    // double min_minor_freq = 40;
    // double minor_amp = 0.5;
    // bool is_main_freq = false;
    // bool is_minor_freq = false;
    // for (const auto& data : fft_gx_data_) {
    //     double freq = data.first;
    //     double amp = data.second;
    //     if (freq >= min_main_freq && freq <= max_main_freq) {
    //         if (amp > main_amp) {
    //             is_main_freq = true;
    //         }
    //     }
    //     if (freq >= min_minor_freq && freq <= max_minor_freq) {
    //         if (amp > minor_amp) {
    //             is_minor_freq = true;
    //         }
    //     }
    // }
    // is_freq_wheel_slip_ = is_main_freq && is_minor_freq;
}

void SlipDetector::CalculateMovingSlipRatio(double current_time)
{
    auto imu_displacement = CalculateIMUDisplacement();
    imu_displacement_ = imu_displacement.norm();
    odom_displacement_ = CalculateOdomDisplacement();
    displacement_diff_ = fabs(odom_displacement_) - fabs(imu_displacement_);
    double min_positive_velocity_slip_displacement = 0.1;
    double min_negative_velocity_slip_displacement = 0.07;
    double min_moving_slip_time = 1.0;
    double check_moving_slip_time = 0.2;
    if (fabs(current_time - last_check_moving_slip_time_) > check_moving_slip_time)
    {
        moving_slip_start_time_ = current_time;
    }
    last_check_moving_slip_time_ = current_time;

    auto back_data = slip_data_buffer_.back();
    double min_linear_velocity = 0.05;
    if (fabs(back_data.odom_linear_velocity) < min_linear_velocity)
    {
        moving_slip_start_time_ = -1.0;
        moving_slip_ratio_ = 0.0;
        positive_displacement_start_time_ = -1.0;
        return;
    }

    double positive_displacement_duration = 0.2;
    double positive_displacement = 0.05;
    if (displacement_diff_ > positive_displacement)
    {
        if (positive_displacement_start_time_ < 0)
        {
            positive_displacement_start_time_ = current_time;
        }
        else
        {
            double diff_time = current_time - positive_displacement_start_time_;
            if (diff_time > positive_displacement_duration)
            {
                positive_displacement_time_ = current_time;
            }
        }
    }
    else
    {
        positive_displacement_start_time_ = -1.0;
    }

    double positive_displacement_diff_time = 3.0;
    if (displacement_diff_ > min_positive_velocity_slip_displacement ||
        (displacement_diff_ < -min_negative_velocity_slip_displacement &&
         positive_displacement_time_ > 1e-6 &&
         (current_time - positive_displacement_time_ < positive_displacement_diff_time)))
    {
        if (moving_slip_start_time_ < 0)
        {
            moving_slip_start_time_ = current_time;
        }
        double diff_time = current_time - moving_slip_start_time_;
        if (diff_time > min_moving_slip_time)
        {
            moving_slip_ratio_ = 1.0;
        }
        else if (diff_time < 0)
        {
            moving_slip_start_time_ = -1.0;
            moving_slip_ratio_ = 0.0;
        }
        else
        {
            moving_slip_ratio_ = diff_time / min_moving_slip_time;
        }
    }
    else
    {
        moving_slip_start_time_ = -1.0;
        moving_slip_ratio_ = 0.0;
    }
}

double SlipDetector::CalculateOdomDisplacement() {
    if (slip_data_buffer_.size() < 2) {
        return 0.0;
    }
    double odom_displacement = 0.0;
    for (size_t i = 1; i < slip_data_buffer_.size(); ++i)
    {
        const auto &cur_data = slip_data_buffer_[i];
        const auto &pre_data = slip_data_buffer_[i - 1];
        double cur_odom_linear_velocity = cur_data.odom_linear_velocity;
        double cur_odom_displacement = cur_odom_linear_velocity * (cur_data.time - pre_data.time);
        odom_displacement += cur_odom_displacement;
    }
    return odom_displacement;
}

void SlipDetector::CalculateTurningSlipRatio(double current_time)
{
    turning_slip_ratio_ = 0;
    if (slip_data_buffer_.size() < 2)
    {
        return;
    }
    // calculate turning slip ratio
    double odom_angle_diff = 0.0;
    for (int i = slip_data_buffer_.size() - 1; i >= 1; i--)
    {
        const auto &cur_data = slip_data_buffer_[i];
        const auto &pre_data = slip_data_buffer_[i - 1];
        double cur_odom_angle = cur_data.odom_angular_velocity * (cur_data.time - pre_data.time);
        odom_angle_diff += cur_odom_angle;
    }
    odom_angle_diff = fescue_iox::NormalizeAngle(odom_angle_diff);
    double imu_angle_diff = fescue_iox::NormalizeAngle(slip_data_buffer_.back().imu_yaw - slip_data_buffer_.front().imu_yaw);
    if (odom_angle_diff * imu_angle_diff > 0 && fabs(odom_angle_diff) < fabs(imu_angle_diff))
    {
        odom_imu_angle_diff_ = 0.0;
    }
    else
    {
        odom_imu_angle_diff_ = odom_angle_diff - imu_angle_diff;
    }
    double min_turning_slip_angle_diff = 0.5;
    double max_turning_slip_time = 2.0;
    if (odom_imu_angle_diff_ < min_turning_slip_angle_diff)
    {
        turning_slip_start_time_ = -1.0;
        turning_slip_ratio_ = 0.0;
    }
    else
    {
        if (turning_slip_start_time_ < 0)
        {
            turning_slip_start_time_ = current_time;
            turning_slip_ratio_ = 0.0;
        }
        else
        {
            double diff_time = current_time - turning_slip_start_time_;
            if (diff_time > max_turning_slip_time)
            {
                diff_time = max_turning_slip_time;
            }
            else if (diff_time < 0)
            {
                diff_time = 0.0;
                turning_slip_start_time_ = -1.0;
            }
            turning_slip_ratio_ = diff_time / max_turning_slip_time;
        }
    }
    const auto &back_data = slip_data_buffer_.back();
    double max_turning_slip_linear_velocity = 0.1;
    double max_ratio_with_linear_velocity = 0.9;
    if (back_data.odom_linear_velocity > max_turning_slip_linear_velocity && turning_slip_ratio_ > max_ratio_with_linear_velocity)
    {
        turning_slip_ratio_ = max_ratio_with_linear_velocity;
    }
}

void SlipDetector::Reset()
{
    imu_filter_ = nullptr;
    turning_slip_ratio_ = 0.0;
    moving_slip_ratio_ = 0.0;
    slip_data_buffer_.clear();
    odom_imu_angle_diff_ = 0.0;
    turning_slip_start_time_ = -1.0;
    moving_slip_start_time_ = -1.0;
    imu_displacement_ = 0.0;
    odom_displacement_ = 0.0;
    displacement_diff_ = 0.0;
    fft_gx_data_.clear();
    fft_gy_data_.clear();
    fft_gz_data_.clear();
    is_freq_wheel_slip_ = false;
    no_motion_start_time_ = -1.0;
    positive_displacement_start_time_ = -1.0;
    positive_displacement_time_ = -1.0;
}

Eigen::Vector2d SlipDetector::CalculateIMUDisplacement() const
{
    if (slip_data_buffer_.size() < 2)
        return Eigen::Vector2d::Zero();
    // 初始化速度
    Eigen::Vector2d velocity_world(
        slip_data_buffer_[0].odom_linear_velocity * cos(slip_data_buffer_[0].imu_yaw),
        slip_data_buffer_[0].odom_linear_velocity * sin(slip_data_buffer_[0].imu_yaw));
    Eigen::Vector2d displacement = Eigen::Vector2d::Zero();
    for (size_t i = 1; i < slip_data_buffer_.size(); ++i)
    {
        const double dt = slip_data_buffer_[i].time - slip_data_buffer_[i - 1].time;
        if (dt < 1e-6)
            continue;
        // 1. 将机体加速度转换到世界坐标系
        const double current_yaw = slip_data_buffer_[i].imu_yaw;
        const Eigen::Vector2d accel_world(
            slip_data_buffer_[i].ax * cos(current_yaw) - slip_data_buffer_[i].ay * sin(current_yaw),
            slip_data_buffer_[i].ax * sin(current_yaw) + slip_data_buffer_[i].ay * cos(current_yaw));
        // 2. 更新世界坐标系速度
        velocity_world += accel_world * dt;
        // 3. 累积位移
        displacement += velocity_world * dt;
    }
    return displacement;
}

} // namespace fescue_iox