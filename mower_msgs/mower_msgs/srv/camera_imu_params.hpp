#pragma once
#include <cstdint>
#include <iomanip>
#include <iox/string.hpp>

namespace mower_msgs::srv
{

struct IMUCalibParams
{
    int32_t imu_calib_result; // 1:IMU对齐相机 2:IMU对齐mowerfront 3:IMU对齐ground
    float trans_front_imu_x;  // IMU到mower MowerFront外参x（unit:m）
    float trans_front_imu_y;  // IMU到mower MowerFront外参y（unit:m）
    float trans_front_imu_z;  // IMU到mower MowerFront外参z（unit:m）
    float rot_front_imu_qx;   // IMU到mower MowerFront外参qx
    float rot_front_imu_qy;   // IMU到mower MowerFront外参qy
    float rot_front_imu_qz;   // IMU到mower MowerFront外参qz
    float rot_front_imu_qw;   // IMU到mower MowerFront外参qw
    float acc_bias_x;         // bias_ax m/s2
    float acc_bias_y;         // bias_ay m/s2
    float acc_bias_z;         // bias_az m/s2
    float gyr_bias_x;         // bias_gx rad/s
    float gyr_bias_y;         // bias_gy rad/s
    float gyr_bias_z;         // bias_gz rad/s
};

inline std::ostream &operator<<(std::ostream &os, const IMUCalibParams &bp)
{
    os << std::fixed << std::setprecision(6);
    os << "IMU Params:\n"
       << "  imu_calib_result: " << bp.imu_calib_result << " m\n"
       << "  trans_front_imu_x: " << bp.trans_front_imu_x << " m\n"
       << "  trans_front_imu_y: " << bp.trans_front_imu_y << " m\n"
       << "  trans_front_imu_z: " << bp.trans_front_imu_z << " m\n"
       << "  rot_front_imu_qx: " << bp.rot_front_imu_qx << "\n"
       << "  rot_front_imu_qy: " << bp.rot_front_imu_qy << "\n"
       << "  rot_front_imu_qz: " << bp.rot_front_imu_qz << "\n"
       << "  rot_front_imu_qw: " << bp.rot_front_imu_qw << "\n"
       << "  Bias:\n"
       << "    acc_bias_x: " << bp.acc_bias_x << "\n"
       << "    acc_bias_y: " << bp.acc_bias_y << "\n"
       << "    acc_bias_z: " << bp.acc_bias_z << "\n"
       << "    gyr_bias_x: " << bp.gyr_bias_x << "\n"
       << "    gyr_bias_y: " << bp.gyr_bias_y << "\n"
       << "    gyr_bias_z: " << bp.gyr_bias_z << "\n";
    return os;
}

struct CameraImuParamsRequest
{
};

struct CameraImuParamsResponse
{
    IMUCalibParams imu_params;
    bool success{false};
};

} // namespace mower_msgs::srv