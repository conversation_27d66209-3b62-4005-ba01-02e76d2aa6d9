#pragma once

#include "camera_bev_params.hpp"
#include "camera_bev_params_2.hpp"
#include "camera_imu_params.hpp"
#include "iox/vector.hpp"

#include <cstdint>

namespace mower_msgs::srv
{

constexpr int BEV_IMAGE_DATA_MAX = 3072000; // img max size: 1280 x 800 x 3

struct StartCalibRequest
{
    bool save_result{false}; // true 保存标定结果，false 不保存标定结果
    int calib_bev{0};        // 0:不开启标定 1：开启标定
    int calib_imu{0};        // 0:不开启标定 1:IMU对齐相机 2:IMU对齐mowerfront 3:IMU对齐ground
};

struct StartCalibResponse
{
    int success{-1}; // 0-success
};

struct CalibrationBevImage
{
    int width;
    int height;
    int channel;
    int size;
    int type;                                      // 0-jpg, 1-png
    iox::vector<uint8_t, BEV_IMAGE_DATA_MAX> data; // png format
};

struct GetCalibResultRequest
{
};

/*
success --- value
CALIB_SUCCESS = 0;
CALIB_ERR_NO_ALG = -1;
CALIB_ERR_INTRINSIC = -2;
CALIB_ERR_WRITE_EEPROM = -3;
CALIB_ERR_TIMEOUT = -4;
CALIB_ERR_NO_IMAGE = -5;
CALIB_FAIL = -6;
*/
struct GetCalibRequestResponse
{
    int success{-1};
    int32_t IMU_calib_result{-1}; //-1:标定失败 #0:不开启标定 1:IMU对齐相机 2:IMU对齐mowerfront 3:IMU对齐ground
    int32_t BEV_calib_result{-1}; //-1:标定失败 #0:不开启标定 1：BEV标定成功
    BEVCalibParams bev_params;
    BEVCalibParams2 bev_params_2;
    IMUCalibParams imu_params;
    CalibrationBevImage image;
};

} // namespace mower_msgs::srv
