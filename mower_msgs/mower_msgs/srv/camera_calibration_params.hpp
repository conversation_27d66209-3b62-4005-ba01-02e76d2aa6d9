#pragma once

#include "camera_bev_params.hpp"
#include "camera_bev_params_2.hpp"
#include "camera_imu_params.hpp"

namespace mower_msgs::srv
{

struct CameraCalibParamsRequest
{
};

struct CameraCalibParamsResponse
{
    bool success_bev{false};
    bool success_bev2{false};
    bool success_imu{false};
    BEVCalibParams bev_params;
    BEVCalibParams2 bev_params_2;
    IMUCalibParams imu_params;
};

} // namespace mower_msgs::srv
