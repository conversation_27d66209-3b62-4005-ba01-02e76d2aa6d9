#pragma once
#include <cstdint>
#include <iox/string.hpp>

namespace mower_msgs::srv
{
enum class TestModeType : int
{
    ENTER = 0,
    EXIT = 1,
};

struct TestModeRequest
{
    uint64_t timestamp;
    TestModeType test_mode_type;
};

struct TestModeResponse
{
    uint64_t timestamp;
    bool success{false};
};

inline std::string asStringLiteral(TestModeType request_type)
{
    switch (request_type)
    {
    case TestModeType::ENTER:
        return "ENTER";
    case TestModeType::EXIT:
        return "EXIT";
    default:
        return "UNKNOWN";
    }
}

} // namespace mower_msgs::srv
