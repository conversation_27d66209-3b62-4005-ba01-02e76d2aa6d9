#pragma once
#include <cstdint>
#include <iomanip>
#include <iox/string.hpp>

namespace mower_msgs::srv
{
/**
 * 感知模组外参
 */
struct BEVCalibParams2
{
    float trans_front_cam_x; // 相机到mower MowerFront外参x（unit:m）
    float trans_front_cam_y; // 相机到mower MowerFront外参y（unit:m）
    float trans_front_cam_z; // 相机到mower MowerFront外参z（unit:m）
    float rot_front_cam_qx;  // 相机到mower MowerFront外参qx
    float rot_front_cam_qy;  // 相机到mower MowerFront外参qy
    float rot_front_cam_qz;  // 相机到mower MowerFront外参qz
    float rot_front_cam_qw;  // 相机到mower MowerFront外参qw
};

inline std::ostream &operator<<(std::ostream &os, const BEVCalibParams2 &bp)
{
    os << std::fixed << std::setprecision(6);
    os << "BEV2 Params:\n"
       << "  trans_front_cam_x: " << bp.trans_front_cam_x << " m\n"
       << "  trans_front_cam_y: " << bp.trans_front_cam_y << " m\n"
       << "  trans_front_cam_z: " << bp.trans_front_cam_z << " m\n"
       << "  rot_front_cam_qx: " << bp.rot_front_cam_qx << " \n"
       << "  rot_front_cam_qy: " << bp.rot_front_cam_qy << " \n"
       << "  rot_front_cam_qz: " << bp.rot_front_cam_qz << " \n"
       << "  rot_front_cam_qw: " << bp.rot_front_cam_qw << " \n";
    return os;
}

} // namespace mower_msgs::srv
